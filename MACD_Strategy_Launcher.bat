@echo off
REM ========================================================================
REM MACD Advanced Trading Strategy - Main Launcher
REM ========================================================================
REM Master control panel for the MACD trading strategy system
REM ========================================================================

:MENU
cls
echo.
echo ========================================================================
echo                    MACD ADVANCED TRADING STRATEGY
echo                           CONTROL PANEL
echo ========================================================================
echo.
echo                        Current Status:
if exist "venv\Scripts\activate.bat" (
    echo                    Environment: ✓ Ready
) else (
    echo                    Environment: ✗ Not Setup
)

if exist "logs\macd_strategy.log" (
    echo                    Last Run: ✓ Available
) else (
    echo                    Last Run: - Never
)

echo.
echo ========================================================================
echo.
echo Please select an option:
echo.
echo   [1] Setup Environment (First Time Setup)
echo   [2] Test System (Verify Everything Works)
echo   [3] Run Strategy (Start Trading)
echo   [4] View Logs (Check Recent Activity)
echo   [5] Quick MT5 Connection Test
echo   [6] Help & Documentation
echo   [7] Exit
echo.
echo ========================================================================
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto SETUP
if "%choice%"=="2" goto TEST
if "%choice%"=="3" goto RUN
if "%choice%"=="4" goto LOGS
if "%choice%"=="5" goto MT5TEST
if "%choice%"=="6" goto HELP
if "%choice%"=="7" goto EXIT

echo Invalid choice. Please try again.
timeout /t 2 /nobreak >nul
goto MENU

:SETUP
cls
echo.
echo ========================================================================
echo                        ENVIRONMENT SETUP
echo ========================================================================
echo.
echo This will install all required dependencies...
echo.
pause
call setup_environment.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto MENU

:TEST
cls
echo.
echo ========================================================================
echo                         SYSTEM TESTING
echo ========================================================================
echo.
echo This will run comprehensive system tests...
echo.
pause
call test_system.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto MENU

:RUN
cls
echo.
echo ========================================================================
echo                        STRATEGY EXECUTION
echo ========================================================================
echo.
echo ⚠️  IMPORTANT CHECKLIST:
echo.
echo   ✓ MetaTrader 5 is running and logged in
echo   ✓ 'DEX 900 DOWN Index' symbol is in Market Watch
echo   ✓ Algorithmic trading is enabled in MT5
echo   ✓ You have reviewed the strategy settings
echo.
echo The strategy will run in SIMULATION MODE by default.
echo No real trades will be executed unless you change the settings.
echo.
set /p confirm="Are you ready to start the strategy? (Y/N): "
if /i "%confirm%"=="Y" (
    call run_macd_strategy.bat
) else (
    echo Strategy start cancelled.
    timeout /t 2 /nobreak >nul
)
echo.
echo Press any key to return to main menu...
pause >nul
goto MENU

:LOGS
cls
echo.
echo ========================================================================
echo                           LOG VIEWER
echo ========================================================================
echo.
call view_logs.bat
echo.
echo Press any key to return to main menu...
pause >nul
goto MENU

:MT5TEST
cls
echo.
echo ========================================================================
echo                      MT5 CONNECTION TEST
echo ========================================================================
echo.
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Environment not setup. Please run option 1 first.
    echo.
    pause
    goto MENU
)

call venv\Scripts\activate.bat
python test_mt5_connection.py
echo.
echo Press any key to return to main menu...
pause >nul
goto MENU

:HELP
cls
echo.
echo ========================================================================
echo                      HELP & DOCUMENTATION
echo ========================================================================
echo.
echo MACD Advanced Trading Strategy System
echo.
echo QUICK START GUIDE:
echo   1. Run 'Setup Environment' (first time only)
echo   2. Ensure MetaTrader 5 is running and logged in
echo   3. Add 'DEX 900 DOWN Index' to Market Watch
echo   4. Run 'Test System' to verify everything works
echo   5. Run 'Strategy' to start trading
echo.
echo IMPORTANT FILES:
echo   - README.md: Comprehensive documentation
echo   - setup_instructions.md: Detailed setup guide
echo   - config\settings.py: Strategy configuration
echo   - logs\: All execution logs
echo.
echo SAFETY FEATURES:
echo   - Simulation mode enabled by default
echo   - 1%% risk per trade maximum
echo   - Comprehensive logging
echo   - Risk management validation
echo.
echo SUPPORT:
echo   - Check logs for detailed information
echo   - Review README.md for troubleshooting
echo   - All trades are logged for analysis
echo.
echo For detailed documentation, see README.md
echo.
echo Press any key to return to main menu...
pause >nul
goto MENU

:EXIT
cls
echo.
echo ========================================================================
echo                    MACD TRADING STRATEGY
echo ========================================================================
echo.
echo Thank you for using the MACD Advanced Trading Strategy!
echo.
echo Remember:
echo   - Always test on demo accounts first
echo   - Monitor your trades regularly
echo   - Check logs for detailed information
echo   - Trade responsibly
echo.
echo Good luck with your trading!
echo.
echo ========================================================================
echo.
pause
exit

REM Error handling
:ERROR
echo.
echo An error occurred. Please check the logs for details.
echo.
pause
goto MENU
