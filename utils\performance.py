"""
Performance tracking and analysis module
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
from utils.logger import setup_logger, log_performance

logger = setup_logger('performance')

class PerformanceTracker:
    """
    Tracks and analyzes trading performance
    """
    
    def __init__(self):
        self.trades = []
        self.equity_curve = []
        self.daily_returns = []
        self.signal_journal = []  # Track all signals, not just executed trades
        
    def add_trade(self, trade_info):
        """
        Add a completed trade to performance tracking
        
        Args:
            trade_info (dict): Trade information
        """
        try:
            trade = {
                'timestamp': trade_info.get('timestamp', datetime.now()),
                'symbol': trade_info.get('symbol'),
                'type': trade_info.get('type'),
                'volume': trade_info.get('volume'),
                'entry_price': trade_info.get('entry_price'),
                'exit_price': trade_info.get('exit_price'),
                'profit': trade_info.get('profit'),
                'commission': trade_info.get('commission', 0),
                'swap': trade_info.get('swap', 0),
                'duration': trade_info.get('duration'),
                'signal_strength': trade_info.get('signal_strength', 0)
            }
            
            self.trades.append(trade)
            logger.info(f"Trade added to performance tracking: {trade['profit']:.2f}")
            
        except Exception as e:
            logger.error(f"Error adding trade to performance tracking: {str(e)}")

    def add_signal(self, signal_info):
        """
        Add a signal to the journal for comprehensive analysis

        Args:
            signal_info (dict): Signal information including execution status
        """
        try:
            signal = {
                'timestamp': signal_info.get('timestamp', datetime.now()),
                'cycle': signal_info.get('cycle', 0),
                'signal': signal_info.get('signal', 'UNKNOWN'),
                'strength': signal_info.get('strength', 0.0),
                'ai_confidence': signal_info.get('ai_confidence', 0.0),
                'reason': signal_info.get('reason', ''),
                'executed': signal_info.get('executed', False),
                'rejection_reason': signal_info.get('rejection_reason', ''),
                'current_price': signal_info.get('current_price', 0),
                'spread': signal_info.get('spread', 0)
            }

            self.signal_journal.append(signal)
            logger.info(f"Signal added to journal: {signal['signal']} (Cycle: {signal['cycle']})")

        except Exception as e:
            logger.error(f"Error adding signal to journal: {str(e)}")
    
    def calculate_metrics(self):
        """
        Calculate comprehensive performance metrics
        
        Returns:
            dict: Performance metrics
        """
        try:
            # Check if we have signal data even if no trades executed
            if not self.trades and not self.signal_journal:
                return {'error': 'No trades or signals available for analysis'}

            # If no executed trades but have signals, analyze signals
            if not self.trades and self.signal_journal:
                return self._analyze_signals_only()
            
            df = pd.DataFrame(self.trades)
            
            # Basic metrics
            total_trades = len(df)
            winning_trades = len(df[df['profit'] > 0])
            losing_trades = len(df[df['profit'] < 0])
            
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # Profit metrics
            total_profit = df['profit'].sum()
            gross_profit = df[df['profit'] > 0]['profit'].sum()
            gross_loss = abs(df[df['profit'] < 0]['profit'].sum())
            
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Average metrics
            avg_win = df[df['profit'] > 0]['profit'].mean() if winning_trades > 0 else 0
            avg_loss = df[df['profit'] < 0]['profit'].mean() if losing_trades > 0 else 0
            
            avg_trade = df['profit'].mean()
            
            # Risk metrics
            returns = df['profit'].values
            sharpe_ratio = self._calculate_sharpe_ratio(returns)
            max_drawdown = self._calculate_max_drawdown(returns)
            
            # Consecutive metrics
            max_consecutive_wins = self._calculate_consecutive_wins(df['profit'])
            max_consecutive_losses = self._calculate_consecutive_losses(df['profit'])
            
            # Duration metrics
            if 'duration' in df.columns and not df['duration'].isna().all():
                avg_trade_duration = df['duration'].mean()
            else:
                avg_trade_duration = None
            
            metrics = {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_profit': total_profit,
                'gross_profit': gross_profit,
                'gross_loss': gross_loss,
                'profit_factor': profit_factor,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'avg_trade': avg_trade,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'max_consecutive_wins': max_consecutive_wins,
                'max_consecutive_losses': max_consecutive_losses,
                'avg_trade_duration': avg_trade_duration
            }
            
            # Log key metrics
            log_performance('win_rate', win_rate)
            log_performance('profit_factor', profit_factor)
            log_performance('total_profit', total_profit)
            log_performance('max_drawdown', max_drawdown)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
            return {'error': str(e)}

    def _analyze_signals_only(self):
        """
        Analyze signals when no trades were executed

        Returns:
            dict: Signal analysis metrics
        """
        try:
            if not self.signal_journal:
                return {'error': 'No signals available for analysis'}

            df = pd.DataFrame(self.signal_journal)

            # Signal statistics
            total_signals = len(df)
            buy_signals = len(df[df['signal'] == 'BUY'])
            sell_signals = len(df[df['signal'] == 'SELL'])
            hold_signals = len(df[df['signal'] == 'HOLD'])

            # Execution statistics
            executed_signals = len(df[df['executed'] == True])
            rejected_signals = len(df[df['executed'] == False])

            # Quality metrics
            avg_signal_strength = df['strength'].mean() if 'strength' in df.columns else 0
            avg_ai_confidence = df['ai_confidence'].mean() if 'ai_confidence' in df.columns else 0

            # Rejection analysis
            rejection_reasons = df[df['executed'] == False]['rejection_reason'].value_counts().to_dict()

            metrics = {
                'analysis_type': 'signals_only',
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'hold_signals': hold_signals,
                'executed_signals': executed_signals,
                'rejected_signals': rejected_signals,
                'execution_rate': (executed_signals / total_signals * 100) if total_signals > 0 else 0,
                'avg_signal_strength': avg_signal_strength,
                'avg_ai_confidence': avg_ai_confidence,
                'rejection_reasons': rejection_reasons,
                'signal_bias': 'BULLISH' if buy_signals > sell_signals else 'BEARISH' if sell_signals > buy_signals else 'NEUTRAL'
            }

            return metrics

        except Exception as e:
            logger.error(f"Error analyzing signals: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """
        Calculate Sharpe ratio
        """
        try:
            if len(returns) == 0:
                return 0
            
            excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate
            
            if np.std(excess_returns) == 0:
                return 0
            
            return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {str(e)}")
            return 0
    
    def _calculate_max_drawdown(self, returns):
        """
        Calculate maximum drawdown
        """
        try:
            cumulative = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = cumulative - running_max
            
            return abs(np.min(drawdown))
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {str(e)}")
            return 0
    
    def _calculate_consecutive_wins(self, profits):
        """
        Calculate maximum consecutive wins
        """
        try:
            max_consecutive = 0
            current_consecutive = 0
            
            for profit in profits:
                if profit > 0:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            
            return max_consecutive
            
        except Exception as e:
            logger.error(f"Error calculating consecutive wins: {str(e)}")
            return 0
    
    def _calculate_consecutive_losses(self, profits):
        """
        Calculate maximum consecutive losses
        """
        try:
            max_consecutive = 0
            current_consecutive = 0
            
            for profit in profits:
                if profit < 0:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0
            
            return max_consecutive
            
        except Exception as e:
            logger.error(f"Error calculating consecutive losses: {str(e)}")
            return 0
    
    def generate_report(self, save_path='reports'):
        """
        Generate comprehensive performance report
        
        Args:
            save_path (str): Path to save report
        """
        try:
            os.makedirs(save_path, exist_ok=True)
            
            metrics = self.calculate_metrics()
            
            if 'error' in metrics:
                logger.error(f"Cannot generate report: {metrics['error']}")
                return
            
            # Create report
            report = f"""
MACD Trading Strategy Performance Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== SUMMARY METRICS ===
Total Trades: {metrics['total_trades']}
Win Rate: {metrics['win_rate']:.2f}%
Profit Factor: {metrics['profit_factor']:.2f}
Total Profit: ${metrics['total_profit']:.2f}

=== TRADE ANALYSIS ===
Winning Trades: {metrics['winning_trades']}
Losing Trades: {metrics['losing_trades']}
Average Win: ${metrics['avg_win']:.2f}
Average Loss: ${metrics['avg_loss']:.2f}
Average Trade: ${metrics['avg_trade']:.2f}

=== RISK METRICS ===
Sharpe Ratio: {metrics['sharpe_ratio']:.2f}
Maximum Drawdown: ${metrics['max_drawdown']:.2f}
Max Consecutive Wins: {metrics['max_consecutive_wins']}
Max Consecutive Losses: {metrics['max_consecutive_losses']}

=== ADDITIONAL INFO ===
Gross Profit: ${metrics['gross_profit']:.2f}
Gross Loss: ${metrics['gross_loss']:.2f}
"""
            
            # Save text report
            with open(f"{save_path}/performance_report.txt", 'w') as f:
                f.write(report)
            
            # Generate charts
            self._generate_charts(save_path)
            
            logger.info(f"Performance report generated in {save_path}")
            
        except Exception as e:
            logger.error(f"Error generating performance report: {str(e)}")
    
    def _generate_charts(self, save_path):
        """
        Generate performance charts
        """
        try:
            if not self.trades:
                return
            
            df = pd.DataFrame(self.trades)
            
            # Set up the plotting style
            plt.style.use('seaborn-v0_8')
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Equity curve
            cumulative_profit = df['profit'].cumsum()
            axes[0, 0].plot(cumulative_profit.index, cumulative_profit.values)
            axes[0, 0].set_title('Equity Curve')
            axes[0, 0].set_xlabel('Trade Number')
            axes[0, 0].set_ylabel('Cumulative Profit ($)')
            axes[0, 0].grid(True)
            
            # Profit distribution
            axes[0, 1].hist(df['profit'], bins=20, alpha=0.7, edgecolor='black')
            axes[0, 1].set_title('Profit Distribution')
            axes[0, 1].set_xlabel('Profit ($)')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].grid(True)
            
            # Monthly performance
            df['month'] = pd.to_datetime(df['timestamp']).dt.to_period('M')
            monthly_profit = df.groupby('month')['profit'].sum()
            axes[1, 0].bar(range(len(monthly_profit)), monthly_profit.values)
            axes[1, 0].set_title('Monthly Performance')
            axes[1, 0].set_xlabel('Month')
            axes[1, 0].set_ylabel('Profit ($)')
            axes[1, 0].grid(True)
            
            # Win/Loss ratio by signal strength
            if 'signal_strength' in df.columns:
                strength_bins = pd.cut(df['signal_strength'], bins=5)
                win_rate_by_strength = df.groupby(strength_bins).apply(
                    lambda x: (x['profit'] > 0).mean() * 100
                )
                axes[1, 1].bar(range(len(win_rate_by_strength)), win_rate_by_strength.values)
                axes[1, 1].set_title('Win Rate by Signal Strength')
                axes[1, 1].set_xlabel('Signal Strength Bin')
                axes[1, 1].set_ylabel('Win Rate (%)')
                axes[1, 1].grid(True)
            
            plt.tight_layout()
            plt.savefig(f"{save_path}/performance_charts.png", dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.error(f"Error generating charts: {str(e)}")
    
    def export_trades(self, filename='trades_export.csv'):
        """
        Export trades to CSV
        
        Args:
            filename (str): Export filename
        """
        try:
            if not self.trades:
                logger.warning("No trades to export")
                return
            
            df = pd.DataFrame(self.trades)
            df.to_csv(filename, index=False)
            
            logger.info(f"Trades exported to {filename}")
            
        except Exception as e:
            logger.error(f"Error exporting trades: {str(e)}")
