"""
Signal generation module for the MACD Trading Strategy
"""
import pandas as pd
import numpy as np
from config.settings import MIN_HIST_SIZE, CONFIRMATION_BARS, SYNTHETIC_INDEX_MODE, VOLATILITY_FILTER_ENABLED
from utils.logger import setup_logger

logger = setup_logger('signals')

class SignalGenerator:
    """
    Generates trading signals based on multi-timeframe MACD analysis
    """
    
    def __init__(self):
        self.last_signal = "HOLD"
        self.signal_strength = 0.0
        
    def generate_signal(self, day_df, h4_df, h1_df):
        """
        Generate trading signals based on multi-timeframe analysis
        
        Args:
            day_df (pd.DataFrame): Daily timeframe data
            h4_df (pd.DataFrame): 4-hour timeframe data
            h1_df (pd.DataFrame): 1-hour timeframe data
            
        Returns:
            dict: Signal information
        """
        try:
            # Check if we have enough data
            if len(day_df) < 2 or len(h4_df) < 2 or len(h1_df) < 2:
                logger.warning("Insufficient data for signal generation")
                return self._create_signal_dict("HOLD", 0.0, "Insufficient data")
            
            # Daily trend direction
            daily_trend = self._get_daily_trend(day_df)
            
            # 4H MACD crossover
            h4_cross = self._detect_macd_crossover(h4_df)
            
            # 1H confirmation
            h1_confirmation = self._get_h1_confirmation(h1_df)
            
            # Generate signal
            signal, strength, reason = self._evaluate_signal(
                daily_trend, h4_cross, h1_confirmation
            )
            
            self.last_signal = signal
            self.signal_strength = strength
            
            logger.info(f"Signal generated: {signal} (Strength: {strength:.2f}) - {reason}")
            
            return self._create_signal_dict(signal, strength, reason, {
                'daily_trend': daily_trend,
                'h4_cross': h4_cross,
                'h1_confirmation': h1_confirmation
            })
            
        except Exception as e:
            logger.error(f"Error generating signal: {str(e)}")
            return self._create_signal_dict("HOLD", 0.0, f"Error: {str(e)}")
    
    def _get_daily_trend(self, day_df):
        """
        Determine daily trend direction
        """
        try:
            latest = day_df.iloc[-1]
            
            # Multiple trend indicators
            trend_indicators = {
                'price_trend': latest['trend'],
                'macd_position': 1 if latest['macd'] > 0 else -1,
                'macd_momentum': 1 if latest['macd'] > latest['signal'] else -1,
                'trend_strength': latest.get('trend_strength', 0.5)
            }
            
            # Weight the indicators
            weighted_trend = (
                trend_indicators['price_trend'] * 0.4 +
                trend_indicators['macd_position'] * 0.3 +
                trend_indicators['macd_momentum'] * 0.3
            )
            
            if weighted_trend > 0.3:
                return "BULLISH"
            elif weighted_trend < -0.3:
                return "BEARISH"
            else:
                return "NEUTRAL"
                
        except Exception as e:
            logger.error(f"Error determining daily trend: {str(e)}")
            return "NEUTRAL"
    
    def _detect_macd_crossover(self, h4_df):
        """
        Detect MACD crossover on 4H timeframe
        """
        try:
            if len(h4_df) < 2:
                return "NONE"
            
            current = h4_df.iloc[-1]
            previous = h4_df.iloc[-2]
            
            # Bullish crossover
            if (current['macd'] > current['signal'] and 
                previous['macd'] <= previous['signal']):
                return "BULLISH"
            
            # Bearish crossover
            elif (current['macd'] < current['signal'] and 
                  previous['macd'] >= previous['signal']):
                return "BEARISH"
            
            # Check for strong existing trend
            elif current['macd'] > current['signal']:
                return "BULLISH_TREND"
            elif current['macd'] < current['signal']:
                return "BEARISH_TREND"
            
            return "NONE"
            
        except Exception as e:
            logger.error(f"Error detecting MACD crossover: {str(e)}")
            return "NONE"
    
    def _get_h1_confirmation(self, h1_df):
        """
        Get 1H confirmation signals
        """
        try:
            if len(h1_df) < 3:
                return {"valid": False, "reason": "Insufficient data"}
            
            current = h1_df.iloc[-1]
            previous = h1_df.iloc[-2]
            
            # Histogram growing
            hist_growing = current['hist_size'] > previous['hist_size']
            
            # Histogram color
            hist_color = current['hist_color']
            
            # Histogram size above minimum
            hist_size_ok = current['hist_size'] > MIN_HIST_SIZE
            
            # MACD momentum
            macd_momentum = current['macd'] - previous['macd']
            
            # Volume/Volatility confirmation
            volume_ok = True
            if SYNTHETIC_INDEX_MODE and VOLATILITY_FILTER_ENABLED:
                # Use volatility instead of volume for synthetic indices
                if 'atr' in h1_df.columns:
                    avg_volatility = h1_df['atr'].rolling(20).mean().iloc[-1]
                    current_volatility = current.get('atr', avg_volatility)
                    volume_ok = current_volatility > avg_volatility * 0.8  # Lower threshold for volatility
                else:
                    volume_ok = True  # Default to true if no volatility data
            elif 'tick_volume' in current:
                # Traditional volume confirmation for non-synthetic instruments
                avg_volume = h1_df['tick_volume'].rolling(20).mean().iloc[-1]
                volume_ok = current['tick_volume'] > avg_volume * 1.2
            
            return {
                "valid": hist_growing and hist_size_ok,
                "hist_growing": hist_growing,
                "hist_color": hist_color,
                "hist_size_ok": hist_size_ok,
                "macd_momentum": macd_momentum,
                "volume_ok": volume_ok
            }
            
        except Exception as e:
            logger.error(f"Error getting H1 confirmation: {str(e)}")
            return {"valid": False, "reason": f"Error: {str(e)}"}
    
    def _evaluate_signal(self, daily_trend, h4_cross, h1_confirmation):
        """
        Evaluate all conditions and generate final signal
        """
        try:
            # Initialize
            signal = "HOLD"
            strength = 0.0
            reasons = []
            
            # BUY signal conditions
            if (daily_trend == "BULLISH" and 
                h4_cross in ["BULLISH", "BULLISH_TREND"] and
                h1_confirmation["valid"] and 
                h1_confirmation["hist_color"] == "green"):
                
                signal = "BUY"
                strength = self._calculate_signal_strength(
                    daily_trend, h4_cross, h1_confirmation, "BUY"
                )
                reasons.append("Bullish alignment across timeframes")
            
            # SELL signal conditions
            elif (daily_trend == "BEARISH" and 
                  h4_cross in ["BEARISH", "BEARISH_TREND"] and
                  h1_confirmation["valid"] and 
                  h1_confirmation["hist_color"] == "red"):
                
                signal = "SELL"
                strength = self._calculate_signal_strength(
                    daily_trend, h4_cross, h1_confirmation, "SELL"
                )
                reasons.append("Bearish alignment across timeframes")
            
            # Partial signals
            elif daily_trend in ["BULLISH", "BEARISH"]:
                if h4_cross in ["BULLISH", "BEARISH"]:
                    reasons.append("Partial signal - waiting for H1 confirmation")
                else:
                    reasons.append("Daily trend present - waiting for H4 crossover")
            
            else:
                reasons.append("No clear directional bias")
            
            reason = "; ".join(reasons) if reasons else "No signal conditions met"
            
            return signal, strength, reason
            
        except Exception as e:
            logger.error(f"Error evaluating signal: {str(e)}")
            return "HOLD", 0.0, f"Evaluation error: {str(e)}"
    
    def _calculate_signal_strength(self, daily_trend, h4_cross, h1_confirmation, signal_type):
        """
        Calculate signal strength based on confluence
        """
        try:
            strength = 0.0
            
            # Daily trend strength (30%)
            if daily_trend in ["BULLISH", "BEARISH"]:
                strength += 0.3
            
            # H4 crossover strength (40%)
            if h4_cross in ["BULLISH", "BEARISH"]:
                strength += 0.4
            elif h4_cross in ["BULLISH_TREND", "BEARISH_TREND"]:
                strength += 0.2
            
            # H1 confirmation strength (30%)
            if h1_confirmation["valid"]:
                strength += 0.15
                if h1_confirmation["volume_ok"]:
                    strength += 0.1
                if abs(h1_confirmation["macd_momentum"]) > 0.0001:
                    strength += 0.05
            
            return min(strength, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating signal strength: {str(e)}")
            return 0.0
    
    def _create_signal_dict(self, signal, strength, reason, details=None):
        """
        Create standardized signal dictionary
        """
        return {
            "signal": signal,
            "strength": strength,
            "reason": reason,
            "timestamp": pd.Timestamp.now(),
            "details": details or {}
        }
