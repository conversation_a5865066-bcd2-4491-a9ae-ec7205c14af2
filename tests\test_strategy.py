"""
Unit tests for the MACD Trading Strategy
"""
import unittest
import pandas as pd
import numpy as np
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy.indicators import calculate_macd, calculate_rsi
from strategy.trend_analysis import determine_trend
from strategy.signals import SignalGenerator
from trading.risk_manager import RiskManager

class TestIndicators(unittest.TestCase):
    """
    Test technical indicators
    """
    
    def setUp(self):
        """
        Set up test data
        """
        # Create sample OHLC data
        dates = pd.date_range('2023-01-01', periods=100, freq='H')
        np.random.seed(42)
        
        # Generate realistic price data
        close_prices = 100 + np.cumsum(np.random.randn(100) * 0.1)
        high_prices = close_prices + np.random.rand(100) * 0.5
        low_prices = close_prices - np.random.rand(100) * 0.5
        open_prices = close_prices + np.random.randn(100) * 0.1
        
        self.test_data = pd.DataFrame({
            'time': dates,
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'tick_volume': np.random.randint(100, 1000, 100)
        })
        self.test_data.set_index('time', inplace=True)
    
    def test_macd_calculation(self):
        """
        Test MACD calculation
        """
        result = calculate_macd(self.test_data.copy())
        
        # Check if MACD columns are added
        self.assertIn('macd', result.columns)
        self.assertIn('signal', result.columns)
        self.assertIn('hist', result.columns)
        self.assertIn('hist_color', result.columns)
        
        # Check if values are numeric
        self.assertTrue(pd.api.types.is_numeric_dtype(result['macd']))
        self.assertTrue(pd.api.types.is_numeric_dtype(result['signal']))
        self.assertTrue(pd.api.types.is_numeric_dtype(result['hist']))
        
        # Check histogram color logic
        hist_diff = result['hist'].diff()
        expected_color = np.where(hist_diff > 0, 'green', 'red')
        # Skip first value (NaN)
        np.testing.assert_array_equal(
            result['hist_color'].iloc[1:].values, 
            expected_color[1:]
        )
    
    def test_rsi_calculation(self):
        """
        Test RSI calculation
        """
        result = calculate_rsi(self.test_data.copy())
        
        # Check if RSI column is added
        self.assertIn('rsi', result.columns)
        
        # Check RSI bounds (should be between 0 and 100)
        rsi_values = result['rsi'].dropna()
        self.assertTrue((rsi_values >= 0).all())
        self.assertTrue((rsi_values <= 100).all())
    
    def test_trend_analysis(self):
        """
        Test trend analysis
        """
        result = determine_trend(self.test_data.copy())
        
        # Check if trend columns are added
        self.assertIn('trend', result.columns)
        self.assertIn('trend_strength', result.columns)
        
        # Check trend values (should be -1, 0, or 1)
        trend_values = result['trend'].dropna()
        valid_values = [-1, 0, 1]
        self.assertTrue(trend_values.isin(valid_values).all())

class TestSignalGenerator(unittest.TestCase):
    """
    Test signal generation
    """
    
    def setUp(self):
        """
        Set up test data and signal generator
        """
        self.signal_generator = SignalGenerator()
        
        # Create test dataframes with indicators
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        base_data = {
            'open': 100 + np.random.randn(50) * 0.5,
            'high': 101 + np.random.randn(50) * 0.5,
            'low': 99 + np.random.randn(50) * 0.5,
            'close': 100 + np.random.randn(50) * 0.5,
            'macd': np.random.randn(50) * 0.1,
            'signal': np.random.randn(50) * 0.1,
            'hist': np.random.randn(50) * 0.05,
            'trend': np.random.choice([-1, 0, 1], 50),
            'hist_color': np.random.choice(['green', 'red'], 50),
            'hist_size': np.abs(np.random.randn(50) * 0.05)
        }
        
        self.day_df = pd.DataFrame(base_data, index=dates)
        self.h4_df = pd.DataFrame(base_data, index=dates)
        self.h1_df = pd.DataFrame(base_data, index=dates)
    
    def test_signal_generation(self):
        """
        Test signal generation
        """
        result = self.signal_generator.generate_signal(
            self.day_df, self.h4_df, self.h1_df
        )
        
        # Check result structure
        self.assertIn('signal', result)
        self.assertIn('strength', result)
        self.assertIn('reason', result)
        self.assertIn('timestamp', result)
        
        # Check signal values
        valid_signals = ['BUY', 'SELL', 'HOLD']
        self.assertIn(result['signal'], valid_signals)
        
        # Check strength bounds
        self.assertGreaterEqual(result['strength'], 0.0)
        self.assertLessEqual(result['strength'], 1.0)

class TestRiskManager(unittest.TestCase):
    """
    Test risk management
    """
    
    def setUp(self):
        """
        Set up test data and risk manager
        """
        self.risk_manager = RiskManager()
        
        self.account_info = {
            'balance': 10000.0,
            'equity': 10000.0,
            'margin': 0.0,
            'margin_level': 1000.0
        }
        
        self.symbol_info = {
            'point': 0.00001,
            'spread': 2,
            'digits': 5,
            'trade_contract_size': 100000,
            'volume_min': 0.01,
            'volume_max': 100.0,
            'volume_step': 0.01
        }
    
    def test_position_sizing(self):
        """
        Test position size calculation
        """
        entry_price = 1.1000
        stop_loss = 1.0950
        
        result = self.risk_manager.calculate_position_size(
            self.account_info, self.symbol_info, entry_price, stop_loss
        )
        
        # Check result structure
        self.assertIsNotNone(result)
        self.assertIn('position_size', result)
        self.assertIn('risk_amount', result)
        self.assertIn('risk_percentage', result)
        
        # Check position size bounds
        self.assertGreaterEqual(result['position_size'], self.symbol_info['volume_min'])
        self.assertLessEqual(result['position_size'], self.symbol_info['volume_max'])
        
        # Check risk percentage
        self.assertLessEqual(result['risk_percentage'], 2.0)  # Should be around 1%
    
    def test_stop_loss_calculation(self):
        """
        Test stop loss calculation
        """
        # Create test data with ATR
        dates = pd.date_range('2023-01-01', periods=30, freq='H')
        test_data = pd.DataFrame({
            'open': 1.1000 + np.random.randn(30) * 0.001,
            'high': 1.1010 + np.random.randn(30) * 0.001,
            'low': 1.0990 + np.random.randn(30) * 0.001,
            'close': 1.1000 + np.random.randn(30) * 0.001,
            'atr': np.full(30, 0.0020)  # 20 pips ATR
        }, index=dates)
        
        # Test BUY signal stop loss
        stop_loss_buy = self.risk_manager.calculate_stop_loss(test_data, "BUY")
        self.assertIsNotNone(stop_loss_buy)
        self.assertLess(stop_loss_buy, test_data['close'].iloc[-1])
        
        # Test SELL signal stop loss
        stop_loss_sell = self.risk_manager.calculate_stop_loss(test_data, "SELL")
        self.assertIsNotNone(stop_loss_sell)
        self.assertGreater(stop_loss_sell, test_data['close'].iloc[-1])
    
    def test_trade_validation(self):
        """
        Test trade validation
        """
        signal_info = {
            'signal': 'BUY',
            'strength': 0.8,
            'reason': 'Test signal'
        }
        
        market_data = {
            'bid': 1.1000,
            'ask': 1.1002,
            'spread': 0.0002
        }
        
        result = self.risk_manager.validate_trade(
            signal_info, market_data, self.account_info, self.symbol_info
        )
        
        # Check result structure
        self.assertIn('valid', result)
        self.assertIn('reasons', result)
        self.assertIn('warnings', result)
        
        # Should be valid with good test data
        self.assertTrue(result['valid'])

if __name__ == '__main__':
    unittest.main()
