"""
Test script to verify the MACD Trading Strategy system functionality
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add project root to path
sys.path.append('.')

def test_system_functionality():
    """
    Test the main system components without requiring MT5 connection
    """
    print("🚀 Testing MACD Trading Strategy System")
    print("=" * 50)
    
    # Test 1: Import all modules
    print("\n1. Testing Module Imports...")
    try:
        from strategy.indicators import calculate_macd, calculate_rsi, calculate_atr
        from strategy.trend_analysis import determine_trend
        from strategy.signals import SignalGenerator
        from strategy.ai_enhancement import AIEnhancement
        from trading.risk_manager import RiskManager
        from trading.order_manager import OrderManager
        from utils.logger import setup_logger
        from utils.performance import PerformanceTracker
        print("   ✅ All modules imported successfully")
    except Exception as e:
        print(f"   ❌ Module import failed: {e}")
        return False
    
    # Test 2: Create sample data
    print("\n2. Creating Sample Market Data...")
    try:
        # Generate realistic sample data
        dates = pd.date_range('2023-01-01', periods=200, freq='h')
        np.random.seed(42)
        
        # Generate price data with trend
        base_price = 1.1000
        price_changes = np.random.normal(0, 0.0005, 200)
        trend = np.linspace(0, 0.01, 200)  # Slight upward trend
        
        close_prices = base_price + np.cumsum(price_changes) + trend
        high_prices = close_prices + np.random.uniform(0, 0.001, 200)
        low_prices = close_prices - np.random.uniform(0, 0.001, 200)
        open_prices = close_prices + np.random.normal(0, 0.0002, 200)
        
        sample_data = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'tick_volume': np.random.randint(100, 1000, 200)
        }, index=dates)
        
        print(f"   ✅ Sample data created: {len(sample_data)} bars")
        print(f"   📊 Price range: {sample_data['close'].min():.5f} - {sample_data['close'].max():.5f}")
    except Exception as e:
        print(f"   ❌ Sample data creation failed: {e}")
        return False
    
    # Test 3: Test indicators
    print("\n3. Testing Technical Indicators...")
    try:
        # Test MACD calculation
        data_with_macd = calculate_macd(sample_data.copy())
        print(f"   ✅ MACD calculated - Latest values:")
        print(f"      MACD: {data_with_macd['macd'].iloc[-1]:.6f}")
        print(f"      Signal: {data_with_macd['signal'].iloc[-1]:.6f}")
        print(f"      Histogram: {data_with_macd['hist'].iloc[-1]:.6f}")
        
        # Test RSI calculation
        data_with_rsi = calculate_rsi(data_with_macd)
        print(f"   ✅ RSI calculated - Latest value: {data_with_rsi['rsi'].iloc[-1]:.2f}")
        
        # Test ATR calculation
        data_with_atr = calculate_atr(data_with_rsi)
        print(f"   ✅ ATR calculated - Latest value: {data_with_atr['atr'].iloc[-1]:.6f}")
        
    except Exception as e:
        print(f"   ❌ Indicator calculation failed: {e}")
        return False
    
    # Test 4: Test trend analysis
    print("\n4. Testing Trend Analysis...")
    try:
        data_with_trend = determine_trend(data_with_atr)
        latest_trend = data_with_trend['trend'].iloc[-1]
        trend_strength = data_with_trend['trend_strength'].iloc[-1]
        
        trend_desc = {1: "Uptrend", -1: "Downtrend", 0: "Sideways"}
        print(f"   ✅ Trend analysis completed")
        print(f"      Current trend: {trend_desc.get(latest_trend, 'Unknown')} ({latest_trend})")
        print(f"      Trend strength: {trend_strength:.3f}")
        
    except Exception as e:
        print(f"   ❌ Trend analysis failed: {e}")
        return False
    
    # Test 5: Test signal generation
    print("\n5. Testing Signal Generation...")
    try:
        signal_generator = SignalGenerator()
        
        # Create multi-timeframe data (simulate different timeframes)
        day_data = data_with_trend.iloc[::24]  # Daily data
        h4_data = data_with_trend.iloc[::4]    # 4H data
        h1_data = data_with_trend              # 1H data
        
        signal_result = signal_generator.generate_signal(day_data, h4_data, h1_data)
        
        print(f"   ✅ Signal generation completed")
        print(f"      Signal: {signal_result['signal']}")
        print(f"      Strength: {signal_result['strength']:.3f}")
        print(f"      Reason: {signal_result['reason']}")
        
    except Exception as e:
        print(f"   ❌ Signal generation failed: {e}")
        return False
    
    # Test 6: Test risk management
    print("\n6. Testing Risk Management...")
    try:
        risk_manager = RiskManager()
        
        # Mock account and symbol info
        account_info = {
            'balance': 10000.0,
            'equity': 10000.0,
            'margin': 0.0,
            'margin_level': 1000.0
        }
        
        symbol_info = {
            'point': 0.00001,
            'spread': 1,
            'digits': 5,
            'trade_contract_size': 100000,
            'volume_min': 0.01,
            'volume_max': 100.0,
            'volume_step': 0.01
        }
        
        # Test position sizing
        entry_price = data_with_trend['close'].iloc[-1]
        stop_loss = risk_manager.calculate_stop_loss(data_with_trend.tail(50), "BUY")
        
        if stop_loss:
            position_info = risk_manager.calculate_position_size(
                account_info, symbol_info, entry_price, stop_loss
            )
            
            if position_info:
                print(f"   ✅ Risk management completed")
                print(f"      Entry price: {entry_price:.5f}")
                print(f"      Stop loss: {stop_loss:.5f}")
                print(f"      Position size: {position_info['position_size']:.2f} lots")
                print(f"      Risk amount: ${position_info['risk_amount']:.2f}")
                print(f"      Risk percentage: {position_info['risk_percentage']:.2f}%")
            else:
                print("   ⚠️  Position sizing calculation returned None")
        else:
            print("   ⚠️  Stop loss calculation returned None")
            
    except Exception as e:
        print(f"   ❌ Risk management failed: {e}")
        return False
    
    # Test 7: Test AI enhancement (basic)
    print("\n7. Testing AI Enhancement...")
    try:
        ai_enhancement = AIEnhancement()
        
        # Test feature preparation
        features = ai_enhancement.prepare_features(data_with_trend)
        if features is not None:
            print(f"   ✅ AI feature preparation completed")
            print(f"      Features shape: {features.shape}")
            print("   ℹ️  AI model training requires more data and time")
        else:
            print("   ⚠️  AI feature preparation returned None")
            
    except Exception as e:
        print(f"   ❌ AI enhancement failed: {e}")
        return False
    
    # Test 8: Test performance tracking
    print("\n8. Testing Performance Tracking...")
    try:
        performance_tracker = PerformanceTracker()
        
        # Add sample trades
        sample_trades = [
            {
                'timestamp': datetime.now() - timedelta(days=5),
                'symbol': 'DEX 900 DOWN Index',
                'type': 'BUY',
                'volume': 0.1,
                'entry_price': 1.1000,
                'exit_price': 1.1050,
                'profit': 50.0,
                'signal_strength': 0.8
            },
            {
                'timestamp': datetime.now() - timedelta(days=3),
                'symbol': 'DEX 900 DOWN Index',
                'type': 'SELL',
                'volume': 0.1,
                'entry_price': 1.1100,
                'exit_price': 1.1080,
                'profit': 20.0,
                'signal_strength': 0.6
            }
        ]
        
        for trade in sample_trades:
            performance_tracker.add_trade(trade)
        
        metrics = performance_tracker.calculate_metrics()
        
        if 'error' not in metrics:
            print(f"   ✅ Performance tracking completed")
            print(f"      Total trades: {metrics['total_trades']}")
            print(f"      Win rate: {metrics['win_rate']:.1f}%")
            print(f"      Total profit: ${metrics['total_profit']:.2f}")
        else:
            print(f"   ⚠️  Performance calculation error: {metrics['error']}")
            
    except Exception as e:
        print(f"   ❌ Performance tracking failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 System Functionality Test COMPLETED!")
    print("✅ All core components are working correctly")
    print("\n📋 Next Steps:")
    print("   1. Ensure MetaTrader 5 is running and logged in")
    print("   2. Add 'DEX 900 DOWN Index' symbol to Market Watch")
    print("   3. Run: python main.py")
    print("\n⚠️  Note: The system will run in simulation mode by default")
    
    return True

if __name__ == "__main__":
    success = test_system_functionality()
    if success:
        print("\n🚀 System is ready for use!")
    else:
        print("\n❌ System test failed - please check the errors above")
