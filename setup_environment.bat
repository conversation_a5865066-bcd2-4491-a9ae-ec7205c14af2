@echo off
REM ========================================================================
REM MACD Advanced Trading Strategy - Environment Setup
REM ========================================================================
REM This batch file sets up the Python environment and installs dependencies
REM Run this ONCE before using the trading strategy
REM ========================================================================

echo.
echo ========================================================================
echo              MACD TRADING STRATEGY - ENVIRONMENT SETUP
echo ========================================================================
echo.
echo This will set up the Python environment for the MACD trading strategy.
echo.
echo Requirements:
echo   - Python 3.8 or higher installed
echo   - MetaTrader 5 terminal installed
echo   - Internet connection for downloading packages
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH!
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment!
        echo.
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
) else (
    echo Virtual environment already exists.
)

echo.
echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Upgrading pip...
python -m pip install --upgrade pip

echo.
echo Installing core dependencies...
echo This may take a few minutes...
echo.

REM Install core packages
pip install MetaTrader5 pandas numpy matplotlib scikit-learn seaborn plotly

if errorlevel 1 (
    echo.
    echo ERROR: Failed to install core dependencies!
    echo Please check your internet connection and try again.
    echo.
    pause
    exit /b 1
)

echo.
echo Installing TensorFlow for AI enhancement...
pip install tensorflow

if errorlevel 1 (
    echo.
    echo WARNING: TensorFlow installation failed.
    echo The strategy will work without AI enhancement.
    echo.
)

echo.
echo Attempting to install TA-Lib...
pip install TA-Lib

if errorlevel 1 (
    echo.
    echo NOTE: TA-Lib installation failed (this is normal on Windows).
    echo The strategy will use custom indicator implementations.
    echo.
)

echo.
echo ========================================================================
echo                           SETUP COMPLETE
echo ========================================================================
echo.
echo Environment setup completed successfully!
echo.
echo Next steps:
echo   1. Ensure MetaTrader 5 is running and logged in
echo   2. Add 'DEX 900 DOWN Index' to Market Watch
echo   3. Enable algorithmic trading in MT5 settings
echo   4. Run: run_macd_strategy.bat
echo.
echo Testing installation...
echo.

REM Test the installation
python -c "
import sys
print('Testing imports...')
try:
    import MetaTrader5 as mt5
    print('✓ MetaTrader5: OK')
except:
    print('✗ MetaTrader5: FAILED')

try:
    import pandas as pd
    print('✓ Pandas: OK')
except:
    print('✗ Pandas: FAILED')

try:
    import numpy as np
    print('✓ NumPy: OK')
except:
    print('✗ NumPy: FAILED')

try:
    import tensorflow as tf
    print('✓ TensorFlow: OK')
except:
    print('✗ TensorFlow: Not available (AI features disabled)')

try:
    import talib
    print('✓ TA-Lib: OK')
except:
    print('✓ TA-Lib: Using custom implementations')

print('')
print('Installation test completed!')
"

echo.
echo ========================================================================
echo Ready to run the MACD Trading Strategy!
echo Execute: run_macd_strategy.bat
echo ========================================================================
echo.
pause
