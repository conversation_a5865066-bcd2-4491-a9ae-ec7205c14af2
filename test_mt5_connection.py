"""
Quick test to verify MT5 connection and symbol availability
"""
import sys
sys.path.append('.')

from trading.mt5_connector import MT5Connector
from config.settings import SYMBOL

def test_mt5_connection():
    """
    Test MT5 connection and symbol availability
    """
    print("🔌 Testing MetaTrader 5 Connection")
    print("=" * 40)
    
    # Initialize MT5 connector
    mt5 = MT5Connector()
    
    if not mt5.connected:
        print("❌ MT5 connection failed!")
        print("\n📋 Troubleshooting steps:")
        print("   1. Ensure MetaTrader 5 is running")
        print("   2. Log into your demo account")
        print("   3. Enable algorithmic trading in Tools → Options → Expert Advisors")
        return False
    
    print("✅ MT5 connection successful!")
    
    # Get account info
    account_info = mt5.get_account_info()
    if account_info:
        print(f"\n📊 Account Information:")
        print(f"   Login: {account_info['login']}")
        print(f"   Balance: ${account_info['balance']:.2f}")
        print(f"   Equity: ${account_info['equity']:.2f}")
        print(f"   Currency: {account_info['currency']}")
    
    # Test symbol availability
    print(f"\n🔍 Testing symbol: {SYMBOL}")
    symbol_info = mt5.get_symbol_info(SYMBOL)
    
    if symbol_info:
        print("✅ Symbol found and available!")
        print(f"   Point: {symbol_info['point']}")
        print(f"   Spread: {symbol_info['spread']}")
        print(f"   Digits: {symbol_info['digits']}")
        print(f"   Min Volume: {symbol_info['volume_min']}")
        print(f"   Max Volume: {symbol_info['volume_max']}")
        
        # Test current price
        current_price = mt5.get_current_price(SYMBOL)
        if current_price:
            print(f"\n💰 Current Prices:")
            print(f"   Bid: {current_price['bid']:.5f}")
            print(f"   Ask: {current_price['ask']:.5f}")
            print(f"   Spread: {current_price['spread']:.5f}")
        
        # Test data fetching
        print(f"\n📈 Testing data fetch...")
        from config.settings import TIMEFRAMES
        
        for tf_name, tf_value in TIMEFRAMES.items():
            data = mt5.fetch_data(SYMBOL, tf_value, 10)
            if data is not None:
                print(f"   ✅ {tf_name}: {len(data)} bars fetched")
                print(f"      Latest close: {data['close'].iloc[-1]:.5f}")
            else:
                print(f"   ❌ {tf_name}: Failed to fetch data")
        
        print("\n🎉 All tests passed! System is ready for trading.")
        
    else:
        print("❌ Symbol not found!")
        print(f"\n📋 To fix this:")
        print(f"   1. Open MetaTrader 5")
        print(f"   2. Right-click in Market Watch")
        print(f"   3. Select 'Symbols'")
        print(f"   4. Search for '{SYMBOL}'")
        print(f"   5. Add it to Market Watch")
        return False
    
    # Cleanup
    mt5.shutdown()
    return True

if __name__ == "__main__":
    success = test_mt5_connection()
    
    if success:
        print("\n🚀 Ready to run the strategy!")
        print("   Execute: python main.py")
    else:
        print("\n⚠️  Please fix the issues above before running the strategy.")
