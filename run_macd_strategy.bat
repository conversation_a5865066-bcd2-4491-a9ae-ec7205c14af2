@echo off
REM ========================================================================
REM MACD Advanced Trading Strategy - Launcher
REM ========================================================================
REM This batch file runs the MACD trading strategy system
REM Make sure MetaTrader 5 is running and logged into your account
REM ========================================================================

echo.
echo ========================================================================
echo                    MACD ADVANCED TRADING STRATEGY
echo ========================================================================
echo.
echo Starting MACD Trading Strategy System...
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please run setup_environment.bat first to install dependencies.
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if MetaTrader5 is available
echo Checking dependencies...
python -c "import MetaTrader5; print('MetaTrader5: OK')" 2>nul
if errorlevel 1 (
    echo ERROR: MetaTrader5 not available!
    echo Please run setup_environment.bat to install dependencies.
    echo.
    pause
    exit /b 1
)

REM Test MT5 connection before starting
echo.
echo Testing MT5 connection...
python test_mt5_connection.py
if errorlevel 1 (
    echo.
    echo ERROR: MT5 connection test failed!
    echo Please ensure:
    echo   1. MetaTrader 5 is running
    echo   2. You are logged into your account
    echo   3. 'DEX 900 DOWN Index' symbol is in Market Watch
    echo   4. Algorithmic trading is enabled
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================================================
echo                        STARTING STRATEGY
echo ========================================================================
echo.
echo Strategy will run in SIMULATION MODE (safe for testing)
echo.
echo To stop the strategy, press Ctrl+C
echo.
echo Logs will be saved in the 'logs' folder
echo.
echo Starting in 3 seconds...
timeout /t 3 /nobreak >nul

REM Run the main strategy
python main.py

echo.
echo ========================================================================
echo                        STRATEGY STOPPED
echo ========================================================================
echo.
echo Check the logs folder for detailed execution logs:
echo   - logs\macd_strategy.log (main strategy log)
echo   - logs\trades.log (trade execution log)
echo   - logs\performance.log (performance metrics)
echo.

REM Check if any log files were created
if exist "logs\macd_strategy.log" (
    echo Latest log entries:
    echo ----------------------------------------
    powershell "Get-Content 'logs\macd_strategy.log' | Select-Object -Last 10"
    echo ----------------------------------------
)

echo.
echo Strategy execution completed.
echo.
pause
