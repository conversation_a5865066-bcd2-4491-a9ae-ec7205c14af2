"""
Trend analysis module for the MACD Trading Strategy
"""
import pandas as pd
import numpy as np
from config.settings import TREND_PERIOD
from utils.logger import setup_logger

logger = setup_logger('trend_analysis')

def determine_trend(df, period=TREND_PERIOD):
    """
    Determine market trend using Higher Highs/Higher Lows analysis
    
    Args:
        df (pd.DataFrame): DataFrame with OHLC data
        period (int): Period for trend analysis
        
    Returns:
        pd.DataFrame: DataFrame with trend indicators added
    """
    try:
        df = df.copy()
        
        # Rolling highs and lows
        df['high_roll'] = df['high'].rolling(period).max()
        df['low_roll'] = df['low'].rolling(period).min()
        
        # Previous period highs and lows
        df['prev_high_roll'] = df['high_roll'].shift(1)
        df['prev_low_roll'] = df['low_roll'].shift(1)
        
        # Trend determination
        conditions = [
            # Uptrend: Higher highs and higher lows
            (df['high'] == df['high_roll']) & (df['low'] > df['prev_low_roll']),
            # Downtrend: Lower highs and lower lows
            (df['low'] == df['low_roll']) & (df['high'] < df['prev_high_roll'])
        ]
        
        choices = [1, -1]  # 1 for uptrend, -1 for downtrend
        
        df['trend'] = np.select(conditions, choices, default=0)  # 0 for sideways
        
        # Trend strength
        df['trend_strength'] = calculate_trend_strength(df, period)
        
        # Trend consistency
        df['trend_consistency'] = df['trend'].rolling(period).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else 0
        )
        
        logger.info("Trend analysis completed successfully")
        return df
        
    except Exception as e:
        logger.error(f"Error in trend analysis: {str(e)}")
        return df

def calculate_trend_strength(df, period=TREND_PERIOD):
    """
    Calculate trend strength based on price momentum
    
    Args:
        df (pd.DataFrame): DataFrame with OHLC data
        period (int): Period for calculation
        
    Returns:
        pd.Series: Trend strength values
    """
    try:
        # Price change over period
        price_change = df['close'].pct_change(period)
        
        # Volatility (standard deviation of returns)
        volatility = df['close'].pct_change().rolling(period).std()
        
        # Trend strength = abs(price_change) / volatility
        trend_strength = np.abs(price_change) / volatility
        
        # Normalize to 0-1 scale
        trend_strength = np.clip(trend_strength / trend_strength.rolling(50).max(), 0, 1)
        
        return trend_strength
        
    except Exception as e:
        logger.error(f"Error calculating trend strength: {str(e)}")
        return pd.Series(index=df.index, dtype=float)

def identify_support_resistance(df, window=20, min_touches=2):
    """
    Identify support and resistance levels
    
    Args:
        df (pd.DataFrame): DataFrame with OHLC data
        window (int): Window for level identification
        min_touches (int): Minimum touches to confirm level
        
    Returns:
        pd.DataFrame: DataFrame with support/resistance levels
    """
    try:
        df = df.copy()
        
        # Find local highs and lows
        df['local_high'] = df['high'][(df['high'].shift(1) < df['high']) & 
                                      (df['high'].shift(-1) < df['high'])]
        df['local_low'] = df['low'][(df['low'].shift(1) > df['low']) & 
                                    (df['low'].shift(-1) > df['low'])]
        
        # Resistance levels (from local highs)
        resistance_levels = []
        for i in range(len(df)):
            if not pd.isna(df['local_high'].iloc[i]):
                level = df['local_high'].iloc[i]
                # Count touches within tolerance
                tolerance = level * 0.001  # 0.1% tolerance
                touches = ((df['high'] >= level - tolerance) & 
                          (df['high'] <= level + tolerance)).sum()
                
                if touches >= min_touches:
                    resistance_levels.append((df.index[i], level))
        
        # Support levels (from local lows)
        support_levels = []
        for i in range(len(df)):
            if not pd.isna(df['local_low'].iloc[i]):
                level = df['local_low'].iloc[i]
                # Count touches within tolerance
                tolerance = level * 0.001  # 0.1% tolerance
                touches = ((df['low'] >= level - tolerance) & 
                          (df['low'] <= level + tolerance)).sum()
                
                if touches >= min_touches:
                    support_levels.append((df.index[i], level))
        
        # Add nearest support/resistance to dataframe
        df['nearest_resistance'] = np.nan
        df['nearest_support'] = np.nan
        
        for i in range(len(df)):
            current_price = df['close'].iloc[i]
            
            # Find nearest resistance above current price
            resistance_above = [level for _, level in resistance_levels if level > current_price]
            if resistance_above:
                df['nearest_resistance'].iloc[i] = min(resistance_above)
            
            # Find nearest support below current price
            support_below = [level for _, level in support_levels if level < current_price]
            if support_below:
                df['nearest_support'].iloc[i] = max(support_below)
        
        logger.info("Support/Resistance levels identified")
        return df
        
    except Exception as e:
        logger.error(f"Error identifying support/resistance: {str(e)}")
        return df

def calculate_market_structure(df):
    """
    Calculate market structure indicators
    
    Args:
        df (pd.DataFrame): DataFrame with OHLC data
        
    Returns:
        pd.DataFrame: DataFrame with market structure indicators
    """
    try:
        df = df.copy()
        
        # Price position within range
        df['range_position'] = (df['close'] - df['low'].rolling(20).min()) / \
                              (df['high'].rolling(20).max() - df['low'].rolling(20).min())
        
        # Breakout detection
        df['breakout_high'] = df['close'] > df['high'].rolling(20).max().shift(1)
        df['breakout_low'] = df['close'] < df['low'].rolling(20).min().shift(1)
        
        # Market phase
        conditions = [
            df['breakout_high'],
            df['breakout_low'],
            (df['range_position'] > 0.7),
            (df['range_position'] < 0.3)
        ]
        
        choices = ['breakout_up', 'breakout_down', 'range_high', 'range_low']
        df['market_phase'] = np.select(conditions, choices, default='range_middle')
        
        logger.info("Market structure analysis completed")
        return df
        
    except Exception as e:
        logger.error(f"Error in market structure analysis: {str(e)}")
        return df
