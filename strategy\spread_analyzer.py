"""
Spread analysis and forecasting for synthetic indices
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import deque
from utils.logger import setup_logger

logger = setup_logger('spread_analyzer')

class SpreadAnalyzer:
    """
    Analyzes and forecasts spread behavior for synthetic indices
    """
    
    def __init__(self, history_size=100):
        self.spread_history = deque(maxlen=history_size)
        self.timestamp_history = deque(maxlen=history_size)
        
    def add_spread_data(self, spread, timestamp=None):
        """
        Add spread data point to history
        
        Args:
            spread (float): Current spread
            timestamp (datetime): Timestamp of the spread data
        """
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            self.spread_history.append(spread)
            self.timestamp_history.append(timestamp)
            
        except Exception as e:
            logger.error(f"Error adding spread data: {str(e)}")
    
    def get_spread_statistics(self):
        """
        Get current spread statistics
        
        Returns:
            dict: Spread statistics
        """
        try:
            if len(self.spread_history) == 0:
                return {'error': 'No spread data available'}
            
            spreads = list(self.spread_history)
            
            stats = {
                'current': spreads[-1] if spreads else 0,
                'average': np.mean(spreads),
                'median': np.median(spreads),
                'min': np.min(spreads),
                'max': np.max(spreads),
                'std': np.std(spreads),
                'count': len(spreads)
            }
            
            # Recent trend (last 10 data points)
            if len(spreads) >= 10:
                recent_spreads = spreads[-10:]
                older_spreads = spreads[-20:-10] if len(spreads) >= 20 else spreads[:-10]
                
                if older_spreads:
                    recent_avg = np.mean(recent_spreads)
                    older_avg = np.mean(older_spreads)
                    stats['trend'] = 'IMPROVING' if recent_avg < older_avg else 'WORSENING'
                    stats['trend_strength'] = abs(recent_avg - older_avg) / older_avg
                else:
                    stats['trend'] = 'UNKNOWN'
                    stats['trend_strength'] = 0
            else:
                stats['trend'] = 'INSUFFICIENT_DATA'
                stats['trend_strength'] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating spread statistics: {str(e)}")
            return {'error': str(e)}
    
    def predict_spread_improvement(self, target_spread=10.0, lookback_minutes=30):
        """
        Predict when spread might improve to target level
        
        Args:
            target_spread (float): Target spread level
            lookback_minutes (int): Minutes to look back for pattern analysis
            
        Returns:
            dict: Prediction information
        """
        try:
            if len(self.spread_history) < 10:
                return {'prediction': 'INSUFFICIENT_DATA', 'confidence': 0.0}
            
            spreads = list(self.spread_history)
            timestamps = list(self.timestamp_history)
            
            # Check recent trend
            recent_spreads = spreads[-10:]
            current_spread = spreads[-1]
            
            # Simple trend analysis
            if len(recent_spreads) >= 5:
                # Linear regression on recent spreads
                x = np.arange(len(recent_spreads))
                y = np.array(recent_spreads)
                
                # Calculate slope
                slope = np.polyfit(x, y, 1)[0]
                
                if current_spread <= target_spread:
                    return {
                        'prediction': 'TARGET_ACHIEVED',
                        'confidence': 1.0,
                        'current_spread': current_spread,
                        'target_spread': target_spread
                    }
                
                if slope < -0.1:  # Improving trend
                    # Estimate time to reach target
                    time_to_target = (current_spread - target_spread) / abs(slope)
                    confidence = min(0.8, abs(slope) * 10)  # Higher slope = higher confidence
                    
                    return {
                        'prediction': 'IMPROVING',
                        'confidence': confidence,
                        'estimated_minutes': max(1, int(time_to_target)),
                        'current_spread': current_spread,
                        'target_spread': target_spread,
                        'trend_slope': slope
                    }
                
                elif slope > 0.1:  # Worsening trend
                    return {
                        'prediction': 'WORSENING',
                        'confidence': min(0.8, slope * 10),
                        'current_spread': current_spread,
                        'target_spread': target_spread,
                        'trend_slope': slope
                    }
                
                else:  # Stable
                    return {
                        'prediction': 'STABLE',
                        'confidence': 0.5,
                        'current_spread': current_spread,
                        'target_spread': target_spread,
                        'trend_slope': slope
                    }
            
            return {'prediction': 'UNKNOWN', 'confidence': 0.0}
            
        except Exception as e:
            logger.error(f"Error predicting spread improvement: {str(e)}")
            return {'prediction': 'ERROR', 'confidence': 0.0, 'error': str(e)}
    
    def should_wait_for_spread(self, current_spread, max_acceptable_spread=15.0):
        """
        Determine if we should wait for better spread conditions
        
        Args:
            current_spread (float): Current spread
            max_acceptable_spread (float): Maximum acceptable spread
            
        Returns:
            dict: Recommendation
        """
        try:
            if current_spread <= max_acceptable_spread:
                return {
                    'recommendation': 'EXECUTE_NOW',
                    'reason': f'Spread {current_spread:.2f} is within acceptable range',
                    'confidence': 1.0
                }
            
            # Get spread prediction
            prediction = self.predict_spread_improvement(max_acceptable_spread)
            
            if prediction['prediction'] == 'IMPROVING' and prediction['confidence'] > 0.6:
                estimated_wait = prediction.get('estimated_minutes', 10)
                if estimated_wait <= 15:  # Don't wait more than 15 minutes
                    return {
                        'recommendation': 'WAIT',
                        'reason': f'Spread improving, estimated {estimated_wait} minutes to target',
                        'confidence': prediction['confidence'],
                        'estimated_wait_minutes': estimated_wait
                    }
            
            if prediction['prediction'] == 'WORSENING':
                return {
                    'recommendation': 'EXECUTE_NOW',
                    'reason': 'Spread is worsening, execute before it gets worse',
                    'confidence': prediction['confidence']
                }
            
            # Default recommendation based on spread level
            if current_spread > max_acceptable_spread * 1.5:
                return {
                    'recommendation': 'SKIP',
                    'reason': f'Spread {current_spread:.2f} too high, skip this opportunity',
                    'confidence': 0.8
                }
            else:
                return {
                    'recommendation': 'WAIT_SHORT',
                    'reason': f'Spread {current_spread:.2f} slightly high, wait briefly',
                    'confidence': 0.6,
                    'estimated_wait_minutes': 5
                }
            
        except Exception as e:
            logger.error(f"Error determining spread recommendation: {str(e)}")
            return {
                'recommendation': 'ERROR',
                'reason': f'Error in analysis: {str(e)}',
                'confidence': 0.0
            }
    
    def get_optimal_trading_times(self):
        """
        Analyze historical data to find optimal trading times
        
        Returns:
            dict: Optimal trading time analysis
        """
        try:
            if len(self.spread_history) < 50:
                return {'error': 'Insufficient data for time analysis'}
            
            # Convert to DataFrame for easier analysis
            df = pd.DataFrame({
                'spread': list(self.spread_history),
                'timestamp': list(self.timestamp_history)
            })
            
            df['hour'] = df['timestamp'].dt.hour
            df['minute'] = df['timestamp'].dt.minute
            df['day_of_week'] = df['timestamp'].dt.dayofweek
            
            # Analyze by hour
            hourly_stats = df.groupby('hour')['spread'].agg(['mean', 'min', 'max', 'count']).reset_index()
            best_hours = hourly_stats.nsmallest(3, 'mean')['hour'].tolist()
            
            # Analyze by day of week (0=Monday, 6=Sunday)
            daily_stats = df.groupby('day_of_week')['spread'].agg(['mean', 'min', 'max', 'count']).reset_index()
            best_days = daily_stats.nsmallest(3, 'mean')['day_of_week'].tolist()
            
            return {
                'best_hours': best_hours,
                'best_days': best_days,
                'hourly_analysis': hourly_stats.to_dict('records'),
                'daily_analysis': daily_stats.to_dict('records'),
                'overall_best_spread': df['spread'].min(),
                'overall_worst_spread': df['spread'].max(),
                'data_points': len(df)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing optimal trading times: {str(e)}")
            return {'error': str(e)}
