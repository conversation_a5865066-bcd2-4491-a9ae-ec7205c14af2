"""
Technical indicators for the MACD Trading Strategy
"""
import pandas as pd
import numpy as np
try:
    import talib as ta
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("TA-Lib not available. Using custom implementations.")

from config.settings import MACD_FAST, MACD_SLOW, MACD_SIGNAL
from utils.logger import setup_logger

logger = setup_logger('indicators')

def calculate_ema(data, period):
    """
    Calculate Exponential Moving Average
    """
    return data.ewm(span=period, adjust=False).mean()

def calculate_macd_custom(close_prices, fast=12, slow=26, signal=9):
    """
    Custom MACD calculation when TA-Lib is not available
    """
    try:
        # Calculate EMAs
        ema_fast = calculate_ema(close_prices, fast)
        ema_slow = calculate_ema(close_prices, slow)
        
        # MACD line
        macd_line = ema_fast - ema_slow
        
        # Signal line
        signal_line = calculate_ema(macd_line, signal)
        
        # Histogram
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
        
    except Exception as e:
        logger.error(f"Error calculating custom MACD: {str(e)}")
        return None, None, None

def calculate_macd(df, fast=MACD_FAST, slow=MACD_SLOW, signal=MACD_SIGNAL):
    """
    Calculate MACD indicators using TA-Lib or custom implementation
    
    Args:
        df (pd.DataFrame): DataFrame with 'close' column
        fast (int): Fast EMA period
        slow (int): Slow EMA period
        signal (int): Signal line EMA period
        
    Returns:
        pd.DataFrame: DataFrame with MACD indicators added
    """
    try:
        df = df.copy()
        
        if TALIB_AVAILABLE:
            # Use TA-Lib if available
            df['macd'], df['signal'], df['hist'] = ta.MACD(
                df['close'].values, 
                fastperiod=fast,
                slowperiod=slow,
                signalperiod=signal
            )
        else:
            # Use custom implementation
            macd, signal_line, hist = calculate_macd_custom(df['close'], fast, slow, signal)
            df['macd'] = macd
            df['signal'] = signal_line
            df['hist'] = hist
        
        # Additional MACD features
        df['hist_color'] = np.where(df['hist'] > df['hist'].shift(1), 'green', 'red')
        df['hist_size'] = abs(df['hist'])
        df['zero_cross'] = np.where(df['macd'] > 0, 1, -1)
        df['macd_cross'] = np.where(df['macd'] > df['signal'], 1, -1)
        df['macd_cross_change'] = df['macd_cross'].diff()
        
        # MACD momentum
        df['macd_momentum'] = df['macd'].diff()
        df['signal_momentum'] = df['signal'].diff()
        
        logger.info("MACD indicators calculated successfully")
        return df
        
    except Exception as e:
        logger.error(f"Error calculating MACD: {str(e)}")
        return df

def calculate_rsi(df, period=14):
    """
    Calculate RSI indicator
    """
    try:
        if TALIB_AVAILABLE:
            df['rsi'] = ta.RSI(df['close'].values, timeperiod=period)
        else:
            # Custom RSI calculation
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
        
        return df
        
    except Exception as e:
        logger.error(f"Error calculating RSI: {str(e)}")
        return df

def calculate_atr(df, period=14):
    """
    Calculate Average True Range
    """
    try:
        if TALIB_AVAILABLE:
            df['atr'] = ta.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=period)
        else:
            # Custom ATR calculation
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            df['atr'] = true_range.rolling(window=period).mean()
        
        return df
        
    except Exception as e:
        logger.error(f"Error calculating ATR: {str(e)}")
        return df

def calculate_bollinger_bands(df, period=20, std_dev=2):
    """
    Calculate Bollinger Bands
    """
    try:
        if TALIB_AVAILABLE:
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = ta.BBANDS(
                df['close'].values, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev
            )
        else:
            # Custom Bollinger Bands calculation
            df['bb_middle'] = df['close'].rolling(window=period).mean()
            bb_std = df['close'].rolling(window=period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * std_dev)
            df['bb_lower'] = df['bb_middle'] - (bb_std * std_dev)
        
        # Bollinger Band position
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        return df
        
    except Exception as e:
        logger.error(f"Error calculating Bollinger Bands: {str(e)}")
        return df
