"""
Risk management module for the MACD Trading Strategy
"""
import pandas as pd
import numpy as np
from config.settings import RISK_PERCENTAGE, MAX_SPREAD, MIN_MARGIN_LEVEL, ATR_STOP_MULTIPLIER
from utils.logger import setup_logger

logger = setup_logger('risk_manager')

class RiskManager:
    """
    Handles position sizing, risk calculation, and trade validation
    """
    
    def __init__(self):
        self.max_risk_per_trade = RISK_PERCENTAGE
        self.max_spread = MAX_SPREAD
        self.min_margin_level = MIN_MARGIN_LEVEL
        self.max_daily_loss = 0.05  # 5% max daily loss
        self.max_drawdown = 0.20    # 20% max drawdown

    def get_effective_margin_level(self, account_info):
        """
        Get effective margin level with demo account workaround

        Args:
            account_info (dict): Account information

        Returns:
            float: Effective margin level
        """
        try:
            margin_level = account_info.get('margin_level', 0)

            # Demo account workaround - margin_level often returns 0
            if margin_level == 0 or margin_level is None:
                # Calculate effective margin level for demo accounts
                balance = account_info.get('balance', 0)
                margin = account_info.get('margin', 0)

                if margin == 0:
                    # No open positions, assume sufficient margin
                    return 1000.0  # High margin level indicating safety
                else:
                    # Calculate margin level
                    equity = account_info.get('equity', balance)
                    return (equity / margin) * 100 if margin > 0 else 1000.0

            return margin_level

        except Exception as e:
            logger.error(f"Error calculating margin level: {str(e)}")
            return 1000.0  # Safe default
        
    def calculate_position_size(self, account_info, symbol_info, entry_price, stop_loss):
        """
        Calculate position size based on risk management rules
        
        Args:
            account_info (dict): Account information
            symbol_info (dict): Symbol information
            entry_price (float): Entry price
            stop_loss (float): Stop loss price
            
        Returns:
            dict: Position sizing information
        """
        try:
            if not all([account_info, symbol_info, entry_price, stop_loss]):
                logger.error("Missing required information for position sizing")
                return None
            
            # Account balance
            balance = account_info['balance']
            
            # Risk amount
            risk_amount = balance * self.max_risk_per_trade
            
            # Price difference (risk per unit)
            price_diff = abs(entry_price - stop_loss)
            
            if price_diff == 0:
                logger.error("Entry price equals stop loss price")
                return None
            
            # Contract size and point value
            contract_size = symbol_info.get('trade_contract_size', 1)
            point = symbol_info.get('point', 0.00001)
            
            # Calculate position size
            risk_per_unit = price_diff
            position_size = risk_amount / risk_per_unit
            
            # Adjust for contract size
            position_size = position_size / contract_size
            
            # Round to valid volume step
            volume_step = symbol_info.get('volume_step', 0.01)
            position_size = round(position_size / volume_step) * volume_step
            
            # Check volume limits
            min_volume = symbol_info.get('volume_min', 0.01)
            max_volume = symbol_info.get('volume_max', 100.0)
            
            position_size = max(min_volume, min(position_size, max_volume))
            
            # Calculate actual risk
            actual_risk = position_size * risk_per_unit * contract_size
            risk_percentage = (actual_risk / balance) * 100
            
            result = {
                'position_size': position_size,
                'risk_amount': actual_risk,
                'risk_percentage': risk_percentage,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'price_diff': price_diff
            }
            
            logger.info(f"Position size calculated: {position_size:.2f} lots, Risk: ${actual_risk:.2f} ({risk_percentage:.2f}%)")
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return None
    
    def calculate_stop_loss(self, df, signal_type, atr_multiplier=ATR_STOP_MULTIPLIER):
        """
        Calculate stop loss based on ATR and market structure
        
        Args:
            df (pd.DataFrame): Recent price data with indicators
            signal_type (str): "BUY" or "SELL"
            atr_multiplier (float): ATR multiplier for stop loss
            
        Returns:
            float: Stop loss price
        """
        try:
            if len(df) == 0:
                logger.error("No data provided for stop loss calculation")
                return None
            
            current_price = df['close'].iloc[-1]
            
            # ATR-based stop loss
            if 'atr' in df.columns:
                atr = df['atr'].iloc[-1]
                if pd.isna(atr) or atr == 0:
                    atr = df['close'].rolling(14).std().iloc[-1] * 1.5  # Fallback
            else:
                atr = df['close'].rolling(14).std().iloc[-1] * 1.5
            
            # Support/Resistance based stop loss
            if signal_type == "BUY":
                # For buy signals, stop below recent low or support
                recent_low = df['low'].rolling(20).min().iloc[-1]
                atr_stop = current_price - (atr * atr_multiplier)
                support_stop = recent_low * 0.999  # 0.1% below support
                
                stop_loss = max(atr_stop, support_stop)
                
            else:  # SELL
                # For sell signals, stop above recent high or resistance
                recent_high = df['high'].rolling(20).max().iloc[-1]
                atr_stop = current_price + (atr * atr_multiplier)
                resistance_stop = recent_high * 1.001  # 0.1% above resistance
                
                stop_loss = min(atr_stop, resistance_stop)
            
            logger.info(f"Stop loss calculated: {stop_loss:.5f} for {signal_type} signal")
            
            return stop_loss
            
        except Exception as e:
            logger.error(f"Error calculating stop loss: {str(e)}")
            return None
    
    def calculate_take_profit(self, entry_price, stop_loss, risk_reward_ratio=2.0):
        """
        Calculate take profit based on risk-reward ratio
        
        Args:
            entry_price (float): Entry price
            stop_loss (float): Stop loss price
            risk_reward_ratio (float): Risk-reward ratio
            
        Returns:
            float: Take profit price
        """
        try:
            risk = abs(entry_price - stop_loss)
            reward = risk * risk_reward_ratio
            
            if entry_price > stop_loss:  # BUY signal
                take_profit = entry_price + reward
            else:  # SELL signal
                take_profit = entry_price - reward
            
            logger.info(f"Take profit calculated: {take_profit:.5f} (R:R = 1:{risk_reward_ratio})")
            
            return take_profit
            
        except Exception as e:
            logger.error(f"Error calculating take profit: {str(e)}")
            return None
    
    def validate_trade(self, signal_info, market_data, account_info, symbol_info):
        """
        Validate trade before execution
        
        Args:
            signal_info (dict): Signal information
            market_data (dict): Current market data
            account_info (dict): Account information
            symbol_info (dict): Symbol information
            
        Returns:
            dict: Validation result
        """
        try:
            validation_result = {
                'valid': True,
                'reasons': [],
                'warnings': []
            }
            
            # Check signal strength
            if signal_info['strength'] < 0.6:
                validation_result['warnings'].append(f"Low signal strength: {signal_info['strength']:.2f}")
            
            # Check spread (adjusted for synthetic indices)
            current_spread = market_data.get('spread', 0)
            if current_spread > self.max_spread:
                validation_result['valid'] = False
                validation_result['reasons'].append(f"Spread too high: {current_spread:.2f} points (max: {self.max_spread})")

            # Check account equity
            equity = account_info.get('equity', 0)
            balance = account_info.get('balance', 0)

            if equity < balance * 0.8:  # 20% drawdown check
                validation_result['valid'] = False
                validation_result['reasons'].append("Maximum drawdown reached")

            # Check margin level with demo account fix
            margin_level = self.get_effective_margin_level(account_info)
            if margin_level < self.min_margin_level:
                validation_result['valid'] = False
                validation_result['reasons'].append(f"Low margin level: {margin_level:.1f}% (min: {self.min_margin_level}%)")
            
            # Check market hours (if needed)
            # This would depend on your broker and symbol
            
            # Log validation result
            if validation_result['valid']:
                logger.info("Trade validation passed")
                if validation_result['warnings']:
                    logger.warning(f"Trade warnings: {'; '.join(validation_result['warnings'])}")
            else:
                logger.warning(f"Trade validation failed: {'; '.join(validation_result['reasons'])}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating trade: {str(e)}")
            return {'valid': False, 'reasons': [f"Validation error: {str(e)}"], 'warnings': []}
    
    def check_correlation_risk(self, symbol, open_positions):
        """
        Check correlation risk with existing positions
        
        Args:
            symbol (str): Symbol to trade
            open_positions (list): List of open positions
            
        Returns:
            dict: Correlation risk assessment
        """
        try:
            # Simplified correlation check
            # In a real implementation, you would check actual correlations
            
            same_symbol_positions = [pos for pos in open_positions if pos.get('symbol') == symbol]
            
            risk_assessment = {
                'high_risk': False,
                'position_count': len(same_symbol_positions),
                'total_exposure': sum(pos.get('volume', 0) for pos in same_symbol_positions),
                'recommendations': []
            }
            
            if len(same_symbol_positions) >= 3:
                risk_assessment['high_risk'] = True
                risk_assessment['recommendations'].append("Too many positions on same symbol")
            
            if risk_assessment['total_exposure'] > 5.0:  # 5 lots max exposure
                risk_assessment['high_risk'] = True
                risk_assessment['recommendations'].append("High exposure on single symbol")
            
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Error checking correlation risk: {str(e)}")
            return {'high_risk': True, 'recommendations': ['Error in risk assessment']}
