"""
Main execution script for the MACD Trading Strategy
"""
import sys
import os
from datetime import datetime
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import *
from utils.logger import setup_logger
from utils.performance import PerformanceTracker
from trading.mt5_connector import MT5Connector
from trading.risk_manager import RiskManager
from trading.order_manager import OrderManager
from strategy.indicators import calculate_macd, calculate_rsi, calculate_atr
from strategy.trend_analysis import determine_trend, identify_support_resistance
from strategy.signals import SignalGenerator
from strategy.ai_enhancement import AIEnhancement

# Initialize logger
logger = setup_logger('main')

class MACDTradingStrategy:
    """
    Main MACD Trading Strategy class
    """
    
    def __init__(self, simulation_mode=True):
        self.simulation_mode = simulation_mode
        self.mt5 = MT5Connector()
        self.risk_manager = RiskManager()
        self.order_manager = OrderManager(simulation_mode)
        self.signal_generator = SignalGenerator()
        self.ai_enhancement = AIEnhancement()
        self.performance_tracker = PerformanceTracker()
        
        self.running = False
        self.last_signal_time = None
        
    def initialize(self):
        """
        Initialize the trading strategy
        """
        try:
            logger.info("===== MACD TRADING STRATEGY INITIALIZATION =====")
            
            # Check MT5 connection
            if not self.mt5.connected:
                logger.error("MT5 connection failed")
                return False
            
            # Verify symbol availability
            symbol_info = self.mt5.get_symbol_info(SYMBOL)
            if not symbol_info:
                logger.error(f"Symbol {SYMBOL} not available")
                return False
            
            # Get account info
            account_info = self.mt5.get_account_info()
            if account_info:
                logger.info(f"Account Balance: ${account_info['balance']:.2f}")
                logger.info(f"Account Equity: ${account_info['equity']:.2f}")
            
            # Initialize AI model with historical data
            logger.info("Initializing AI enhancement module...")
            h1_data = self.fetch_and_prepare_data('1H')
            if h1_data is not None and len(h1_data) > 100:
                self.ai_enhancement.train_model(h1_data)
            
            logger.info("Strategy initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error during initialization: {str(e)}")
            return False
    
    def fetch_and_prepare_data(self, timeframe_key):
        """
        Fetch and prepare data for analysis
        
        Args:
            timeframe_key (str): Timeframe key ('1H', '4H', '1D')
            
        Returns:
            pd.DataFrame: Prepared data with indicators
        """
        try:
            timeframe = TIMEFRAMES[timeframe_key]
            
            # Determine number of bars based on timeframe
            if timeframe_key == '1H':
                bars = BARS_1H
            elif timeframe_key == '4H':
                bars = BARS_4H
            else:
                bars = BARS_1D
            
            # Fetch data
            df = self.mt5.fetch_data(SYMBOL, timeframe, bars)
            if df is None or len(df) == 0:
                logger.error(f"No data received for {timeframe_key}")
                return None
            
            # Calculate indicators
            df = calculate_macd(df)
            df = calculate_rsi(df)
            df = calculate_atr(df)
            
            # Add trend analysis for daily and 4H
            if timeframe_key in ['1D', '4H']:
                df = determine_trend(df)
                df = identify_support_resistance(df)
            
            logger.info(f"Data prepared for {timeframe_key}: {len(df)} bars")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching data for {timeframe_key}: {str(e)}")
            return None
    
    def analyze_market(self):
        """
        Perform multi-timeframe market analysis
        
        Returns:
            dict: Analysis results
        """
        try:
            logger.info("Starting market analysis...")
            
            # Fetch multi-timeframe data
            day_df = self.fetch_and_prepare_data('1D')
            h4_df = self.fetch_and_prepare_data('4H')
            h1_df = self.fetch_and_prepare_data('1H')
            
            if any(df is None for df in [day_df, h4_df, h1_df]):
                logger.error("Failed to fetch required data")
                return None
            
            # Generate signal
            signal_info = self.signal_generator.generate_signal(day_df, h4_df, h1_df)
            
            # AI enhancement
            if self.ai_enhancement.is_trained:
                ai_confidence = self.ai_enhancement.predict_confidence(h1_df)
                signal_info['ai_confidence'] = ai_confidence
                logger.info(f"AI Confidence: {ai_confidence:.3f}")
            
            # Get current market data
            current_price = self.mt5.get_current_price(SYMBOL)
            
            analysis = {
                'signal_info': signal_info,
                'current_price': current_price,
                'day_df': day_df,
                'h4_df': h4_df,
                'h1_df': h1_df,
                'timestamp': datetime.now()
            }
            
            logger.info(f"Market analysis completed: {signal_info['signal']} (Strength: {signal_info['strength']:.2f})")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in market analysis: {str(e)}")
            return None
    
    def execute_trade(self, analysis):
        """
        Execute trade based on analysis
        
        Args:
            analysis (dict): Market analysis results
        """
        try:
            signal_info = analysis['signal_info']
            current_price = analysis['current_price']
            
            if signal_info['signal'] == 'HOLD':
                logger.info("No trade signal - holding position")
                return
            
            # Get account and symbol info
            account_info = self.mt5.get_account_info()
            symbol_info = self.mt5.get_symbol_info(SYMBOL)
            
            if not account_info or not symbol_info:
                logger.error("Failed to get account or symbol information")
                return
            
            # Calculate stop loss
            stop_loss = self.risk_manager.calculate_stop_loss(
                analysis['h1_df'], 
                signal_info['signal']
            )
            
            if not stop_loss:
                logger.error("Failed to calculate stop loss")
                return
            
            # Calculate position size
            entry_price = current_price['ask'] if signal_info['signal'] == 'BUY' else current_price['bid']
            position_info = self.risk_manager.calculate_position_size(
                account_info, symbol_info, entry_price, stop_loss
            )
            
            if not position_info:
                logger.error("Failed to calculate position size")
                return
            
            # Calculate take profit
            take_profit = self.risk_manager.calculate_take_profit(
                entry_price, stop_loss, risk_reward_ratio=2.0
            )
            
            # Validate trade
            validation = self.risk_manager.validate_trade(
                signal_info, current_price, account_info, symbol_info
            )
            
            if not validation['valid']:
                logger.warning(f"Trade validation failed: {'; '.join(validation['reasons'])}")
                return
            
            # Execute order
            order_result = self.order_manager.place_market_order(
                symbol=SYMBOL,
                order_type=signal_info['signal'],
                volume=position_info['position_size'],
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"MACD Strategy - Strength: {signal_info['strength']:.2f}"
            )
            
            if order_result['success']:
                logger.info(f"Trade executed successfully: {order_result}")
                
                # Update last signal time
                self.last_signal_time = datetime.now()
                
                # Log trade details
                trade_info = {
                    'timestamp': datetime.now(),
                    'symbol': SYMBOL,
                    'type': signal_info['signal'],
                    'volume': position_info['position_size'],
                    'entry_price': order_result['price'],
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'signal_strength': signal_info['strength'],
                    'ai_confidence': signal_info.get('ai_confidence', 0.5)
                }
                
                logger.info(f"Trade logged: {trade_info}")
                
            else:
                logger.error(f"Trade execution failed: {order_result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error executing trade: {str(e)}")
    
    def run_cycle(self):
        """
        Run one complete strategy cycle
        """
        try:
            logger.info("===== STRATEGY CYCLE START =====")
            
            # Analyze market
            analysis = self.analyze_market()
            if not analysis:
                logger.error("Market analysis failed")
                return
            
            # Execute trade if signal present
            self.execute_trade(analysis)
            
            # Update performance tracking
            # (This would be enhanced to track actual trade outcomes)
            
            logger.info("===== STRATEGY CYCLE COMPLETED =====")
            
        except Exception as e:
            logger.error(f"Error in strategy cycle: {str(e)}")
    
    def run(self, cycles=None, interval_minutes=60):
        """
        Run the strategy continuously or for specified cycles
        
        Args:
            cycles (int): Number of cycles to run (None for continuous)
            interval_minutes (int): Minutes between cycles
        """
        try:
            logger.info(f"Starting strategy execution - Simulation Mode: {self.simulation_mode}")
            
            if not self.initialize():
                logger.error("Strategy initialization failed")
                return
            
            self.running = True
            cycle_count = 0
            
            while self.running:
                try:
                    self.run_cycle()
                    cycle_count += 1
                    
                    if cycles and cycle_count >= cycles:
                        logger.info(f"Completed {cycles} cycles")
                        break
                    
                    if self.running:
                        logger.info(f"Waiting {interval_minutes} minutes until next cycle...")
                        time.sleep(interval_minutes * 60)
                    
                except KeyboardInterrupt:
                    logger.info("Strategy stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Error in strategy execution: {str(e)}")
                    time.sleep(60)  # Wait 1 minute before retrying
            
        except Exception as e:
            logger.error(f"Fatal error in strategy execution: {str(e)}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """
        Shutdown the strategy
        """
        try:
            logger.info("Shutting down strategy...")
            
            self.running = False
            
            # Generate performance report
            self.performance_tracker.generate_report()
            
            # Close MT5 connection
            self.mt5.shutdown()
            
            logger.info("Strategy shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")

def main():
    """
    Main entry point
    """
    try:
        # Create strategy instance
        strategy = MACDTradingStrategy(simulation_mode=True)
        
        # Run strategy for 5 cycles (for testing)
        strategy.run(cycles=5, interval_minutes=1)  # 1 minute for testing
        
    except Exception as e:
        logger.error(f"Fatal error in main: {str(e)}")

if __name__ == "__main__":
    main()
