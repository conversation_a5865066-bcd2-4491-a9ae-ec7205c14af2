"""
Logging configuration for the MACD Trading Strategy
"""
import logging
import os
from datetime import datetime
from config.settings import LOG_LEVEL, LOG_FORMAT, LOG_DATE_FORMAT, LOG_FILE

def setup_logger(name='macd_strategy'):
    """
    Set up logger with file and console handlers
    """
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    formatter = logging.Formatter(LOG_FORMAT, datefmt=LOG_DATE_FORMAT)
    
    # File handler
    file_handler = logging.FileHandler(LOG_FILE)
    file_handler.setLevel(getattr(logging, LOG_LEVEL))
    file_handler.setFormatter(formatter)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_trade(symbol, action, price, volume, timestamp=None):
    """
    Log trade execution details
    """
    if timestamp is None:
        timestamp = datetime.now()
    
    trade_logger = logging.getLogger('trade_logger')
    if not trade_logger.handlers:
        # Set up trade logger
        os.makedirs('logs', exist_ok=True)
        handler = logging.FileHandler('logs/trades.log')
        formatter = logging.Formatter('%(asctime)s | %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        handler.setFormatter(formatter)
        trade_logger.addHandler(handler)
        trade_logger.setLevel(logging.INFO)
    
    trade_logger.info(f"{symbol} | {action} | {price} | {volume} | {timestamp}")

def log_performance(metric, value, timestamp=None):
    """
    Log performance metrics
    """
    if timestamp is None:
        timestamp = datetime.now()
    
    perf_logger = logging.getLogger('performance_logger')
    if not perf_logger.handlers:
        # Set up performance logger
        os.makedirs('logs', exist_ok=True)
        handler = logging.FileHandler('logs/performance.log')
        formatter = logging.Formatter('%(asctime)s | %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        handler.setFormatter(formatter)
        perf_logger.addHandler(handler)
        perf_logger.setLevel(logging.INFO)
    
    perf_logger.info(f"{metric} | {value} | {timestamp}")
