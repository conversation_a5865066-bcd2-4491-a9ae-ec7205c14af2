"""
Configuration settings for the MACD Trading Strategy
"""
import MetaTrader5 as mt5

# Trading Symbol
SYMBOL = "DEX 900 DOWN Index"

# Timeframes for multi-timeframe analysis
TIMEFRAMES = {
    '1H': mt5.TIMEFRAME_H1,
    '4H': mt5.TIMEFRAME_H4,
    '1D': mt5.TIMEFRAME_D1
}

# MACD Parameters
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9

# Data Parameters
BARS_1H = 2000
BARS_4H = 1000
BARS_1D = 500

# Risk Management
RISK_PERCENTAGE = 0.01  # 1% risk per trade
MAX_SPREAD = 10  # Maximum spread in points

# AI Model Parameters
LSTM_EPOCHS = 20
LSTM_BATCH_SIZE = 32
LSTM_UNITS = 50

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s | %(levelname)s | %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
LOG_FILE = 'logs/macd_strategy.log'

# Performance Tracking
PERFORMANCE_LOG = 'logs/performance.log'
TRADE_LOG = 'logs/trades.log'

# Trend Analysis Parameters
TREND_PERIOD = 5  # Period for HH/HL analysis
VOLATILITY_THRESHOLD = 0.02  # Minimum volatility for trade execution

# Signal Confirmation Parameters
MIN_HIST_SIZE = 0.0001  # Minimum histogram size for signal
CONFIRMATION_BARS = 2  # Number of bars for signal confirmation
