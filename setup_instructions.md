# MACD Trading Strategy - Setup Instructions

## Quick Start Guide

### Step 1: Verify MetaTrader 5 Setup
1. **Ensure MT5 is running** and logged into your demo account
2. **Add DEX900 DOWN symbol** to Market Watch:
   - Right-click in Market Watch
   - Select "Symbols"
   - Search for "DEX900 DOWN"
   - Add to Market Watch
3. **Enable Algorithmic Trading**:
   - Go to Tools → Options → Expert Advisors
   - Check "Allow algorithmic trading"
   - Check "Allow DLL imports"

### Step 2: Install Dependencies

#### Option A: Standard Installation
```bash
pip install -r requirements.txt
```

#### Option B: If TA-Lib Installation Fails (Windows)
1. Download TA-Lib wheel from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
2. Install the appropriate wheel for your Python version:
```bash
# Example for Python 3.9, 64-bit
pip install TA_Lib-0.4.25-cp39-cp39-win_amd64.whl
pip install MetaTrader5 pandas numpy matplotlib scikit-learn tensorflow seaborn plotly
```

#### Option C: Minimal Installation (if <PERSON><PERSON><PERSON><PERSON> fails)
```bash
pip install MetaTrader5 pandas numpy matplotlib scikit-learn seaborn plotly
# TA-Lib is optional - custom implementations will be used
```

### Step 3: Test the Installation
```bash
# Run unit tests to verify everything works
python tests/test_strategy.py
```

### Step 4: Run the Strategy
```bash
# Run in simulation mode (recommended for first time)
python main.py
```

## Detailed Setup Instructions

### 1. Python Environment Setup

#### Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv macd_trading_env

# Activate virtual environment
# Windows:
macd_trading_env\Scripts\activate
# Linux/Mac:
source macd_trading_env/bin/activate
```

#### Install Dependencies
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 2. MetaTrader 5 Configuration

#### Enable Algorithmic Trading
1. Open MetaTrader 5
2. Go to **Tools → Options**
3. Click **Expert Advisors** tab
4. Enable the following:
   - ✅ Allow algorithmic trading
   - ✅ Allow DLL imports
   - ✅ Allow imports of external experts

#### Add Required Symbol
1. Right-click in **Market Watch** window
2. Select **Symbols**
3. In the search box, type: `DEX900 DOWN`
4. Double-click to add to Market Watch
5. Ensure the symbol shows current prices

#### Verify Account Type
- Ensure you're logged into a **Demo account** for testing
- Check account balance is sufficient (recommended: $10,000+)

### 3. Configuration Customization

#### Basic Configuration (`config/settings.py`)
```python
# Trading Symbol - Change if using different symbol
SYMBOL = "DEX900 DOWN"

# Risk Management - Adjust based on your risk tolerance
RISK_PERCENTAGE = 0.01  # 1% risk per trade

# MACD Parameters - Standard settings
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9
```

#### Logging Configuration
- Logs are automatically created in `logs/` directory
- Adjust `LOG_LEVEL` in settings.py for more/less detail

### 4. Testing and Validation

#### Run Unit Tests
```bash
# Test all components
python -m pytest tests/ -v

# Test specific component
python tests/test_strategy.py
```

#### Test MT5 Connection
```python
# Quick connection test
import MetaTrader5 as mt5

if mt5.initialize():
    print("MT5 connection successful!")
    account_info = mt5.account_info()
    print(f"Account: {account_info.login}")
    print(f"Balance: {account_info.balance}")
    mt5.shutdown()
else:
    print("MT5 connection failed!")
```

### 5. Running the Strategy

#### Simulation Mode (Safe Testing)
```bash
python main.py
```
- All trades are simulated
- No real money at risk
- Full logging and analysis

#### Live Trading Mode (After Testing)
1. Modify `main.py`:
```python
# Change this line:
strategy = MACDTradingStrategy(simulation_mode=False)
```
2. Run with caution:
```bash
python main.py
```

### 6. Monitoring and Analysis

#### Real-time Monitoring
- Watch console output for trade signals
- Monitor log files in `logs/` directory
- Check MT5 terminal for actual trades (if live mode)

#### Performance Analysis
```bash
# Generate performance report
python -c "
from utils.performance import PerformanceTracker
tracker = PerformanceTracker()
tracker.generate_report()
"
```

## Troubleshooting Common Issues

### Issue 1: "MT5 initialization failed"
**Solutions:**
1. Ensure MT5 is running and logged in
2. Check if algorithmic trading is enabled
3. Restart MT5 and try again
4. Verify account has trading permissions

### Issue 2: "Symbol DEX900 DOWN not found"
**Solutions:**
1. Add symbol to Market Watch manually
2. Check symbol name spelling
3. Verify symbol is available with your broker
4. Update `SYMBOL` in config/settings.py if different

### Issue 3: TA-Lib installation fails
**Solutions:**
1. Use pre-compiled wheel (see Option B above)
2. Install Microsoft Visual C++ Build Tools
3. Use conda: `conda install -c conda-forge ta-lib`
4. System will use custom implementations as fallback

### Issue 4: TensorFlow installation fails
**Solutions:**
1. Install CPU-only version: `pip install tensorflow-cpu`
2. Use older version: `pip install tensorflow==2.10.0`
3. Skip TensorFlow - AI features will be disabled
4. Strategy will continue without AI enhancement

### Issue 5: Permission errors
**Solutions:**
1. Run command prompt as Administrator (Windows)
2. Use virtual environment
3. Check antivirus software blocking Python

## Performance Optimization

### For Better Performance:
1. **Use SSD storage** for faster data access
2. **Increase RAM** if running multiple strategies
3. **Stable internet connection** for real-time data
4. **Close unnecessary programs** during trading hours

### For Better Results:
1. **Test thoroughly** on demo account first
2. **Start with small position sizes**
3. **Monitor performance regularly**
4. **Adjust parameters based on market conditions**

## Next Steps After Setup

1. **Run in simulation mode** for at least 1 week
2. **Analyze performance reports** to understand strategy behavior
3. **Optimize parameters** based on historical performance
4. **Gradually increase position sizes** if profitable
5. **Consider adding additional filters** or indicators

## Support and Resources

### Log Files Location:
- `logs/macd_strategy.log` - Main strategy log
- `logs/trades.log` - Trade execution log
- `logs/performance.log` - Performance metrics

### Configuration Files:
- `config/settings.py` - Main configuration
- `requirements.txt` - Python dependencies

### Key Commands:
```bash
# Install dependencies
pip install -r requirements.txt

# Run tests
python tests/test_strategy.py

# Run strategy
python main.py

# Generate performance report
python -c "from utils.performance import PerformanceTracker; PerformanceTracker().generate_report()"
```

Remember: Always test thoroughly before live trading!
