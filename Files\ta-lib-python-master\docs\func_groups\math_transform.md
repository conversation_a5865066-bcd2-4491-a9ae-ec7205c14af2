# Math Transform Functions
### ACOS - Vector Trigonometric ACos
```python
real = ACOS(real)
```

### ASIN - Vector Trigonometric ASin
```python
real = ASIN(real)
```

### ATAN - Vector Trigonometric ATan
```python
real = ATAN(real)
```

### CEIL - Vector Ceil
```python
real = CEIL(real)
```

### COS - Vector Trigonometric Cos
```python
real = COS(real)
```

### COSH - Vector Trigonometric Cosh
```python
real = COSH(real)
```

### EXP - Vector Arithmetic Exp
```python
real = EXP(real)
```

### FLOOR - Vector Floor
```python
real = FLOOR(real)
```

### LN - Vector Log Natural
```python
real = LN(real)
```

### LOG10 - Vector Log10
```python
real = LOG10(real)
```

### SIN - Vector Trigonometric Sin
```python
real = SIN(real)
```

### SINH - Vector Trigonometric Sinh
```python
real = SINH(real)
```

### SQRT - Vector Square Root
```python
real = SQRT(real)
```

### TAN - Vector Trigonometric Tan
```python
real = TAN(real)
```

### TANH - Vector Trigonometric Tanh
```python
real = TANH(real)
```


[Documentation Index](../doc_index.md)

[FLOAT_RIGHTAll Function Groups](../funcs.md)
