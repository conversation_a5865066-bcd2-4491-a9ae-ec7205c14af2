@echo off
REM ========================================================================
REM MACD Advanced Trading Strategy - Log Viewer
REM ========================================================================
REM This batch file displays the latest strategy logs
REM ========================================================================

echo.
echo ========================================================================
echo                    MACD STRATEGY - LOG VIEWER
echo ========================================================================
echo.

REM Check if logs directory exists
if not exist "logs" (
    echo No logs directory found.
    echo The strategy hasn't been run yet.
    echo.
    pause
    exit /b 0
)

REM Main strategy log
if exist "logs\macd_strategy.log" (
    echo ========================================================================
    echo MAIN STRATEGY LOG (Last 20 entries)
    echo ========================================================================
    powershell "Get-Content 'logs\macd_strategy.log' | Select-Object -Last 20"
    echo.
) else (
    echo Main strategy log not found.
    echo.
)

REM Trade log
if exist "logs\trades.log" (
    echo ========================================================================
    echo TRADE EXECUTION LOG (Last 10 entries)
    echo ========================================================================
    powershell "Get-Content 'logs\trades.log' | Select-Object -Last 10"
    echo.
) else (
    echo Trade log not found (no trades executed yet).
    echo.
)

REM Performance log
if exist "logs\performance.log" (
    echo ========================================================================
    echo PERFORMANCE LOG (Last 10 entries)
    echo ========================================================================
    powershell "Get-Content 'logs\performance.log' | Select-Object -Last 10"
    echo.
) else (
    echo Performance log not found.
    echo.
)

REM Log file sizes and dates
echo ========================================================================
echo LOG FILE INFORMATION
echo ========================================================================
if exist "logs\macd_strategy.log" (
    for %%f in ("logs\macd_strategy.log") do echo Main Strategy Log: %%~zf bytes, Modified: %%~tf
)
if exist "logs\trades.log" (
    for %%f in ("logs\trades.log") do echo Trade Log: %%~zf bytes, Modified: %%~tf
)
if exist "logs\performance.log" (
    for %%f in ("logs\performance.log") do echo Performance Log: %%~zf bytes, Modified: %%~tf
)

echo.
echo ========================================================================
echo.
echo To view full logs, open the files in the 'logs' folder:
echo   - logs\macd_strategy.log (main strategy execution)
echo   - logs\trades.log (trade details)
echo   - logs\performance.log (performance metrics)
echo.
echo To monitor logs in real-time while strategy is running:
echo   powershell "Get-Content 'logs\macd_strategy.log' -Wait -Tail 10"
echo.
pause
