"""
MetaTrader 5 connection and data fetching module
"""
import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime
from utils.logger import setup_logger

logger = setup_logger('mt5_connector')

class MT5Connector:
    """
    Handles MetaTrader 5 connection and data operations
    """
    
    def __init__(self):
        self.connected = False
        self.initialize_connection()
    
    def initialize_connection(self):
        """
        Initialize MT5 connection
        """
        try:
            if not mt5.initialize():
                logger.error("MT5 initialization failed")
                return False
            
            self.connected = True
            logger.info("MT5 connection established successfully")
            
            # Log account info
            account_info = mt5.account_info()
            if account_info:
                logger.info(f"Account: {account_info.login}, Balance: {account_info.balance}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize MT5: {str(e)}")
            return False
    
    def fetch_data(self, symbol, timeframe, bars=1000):
        """
        Fetch historical data from MT5
        
        Args:
            symbol (str): Trading symbol
            timeframe: MT5 timeframe constant
            bars (int): Number of bars to fetch
            
        Returns:
            pd.DataFrame: Historical data with OHLCV
        """
        try:
            if not self.connected:
                logger.error("MT5 not connected")
                return None
            
            # Check if symbol is available
            if not mt5.symbol_select(symbol, True):
                logger.error(f"Symbol {symbol} not found or not available")
                return None
            
            # Fetch rates
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
            
            if rates is None or len(rates) == 0:
                logger.error(f"No data received for {symbol}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            logger.info(f"Fetched {len(df)} bars for {symbol} on timeframe {timeframe}")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            return None
    
    def get_current_price(self, symbol):
        """
        Get current bid/ask prices
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            dict: Current prices {'bid': float, 'ask': float, 'spread': float}
        """
        try:
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"Failed to get tick data for {symbol}")
                return None
            
            return {
                'bid': tick.bid,
                'ask': tick.ask,
                'spread': tick.ask - tick.bid,
                'time': datetime.fromtimestamp(tick.time)
            }
            
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {str(e)}")
            return None
    
    def get_symbol_info(self, symbol):
        """
        Get symbol information
        
        Args:
            symbol (str): Trading symbol
            
        Returns:
            dict: Symbol information
        """
        try:
            info = mt5.symbol_info(symbol)
            if info is None:
                logger.error(f"Failed to get symbol info for {symbol}")
                return None
            
            return {
                'point': info.point,
                'spread': info.spread,
                'digits': info.digits,
                'trade_contract_size': info.trade_contract_size,
                'volume_min': info.volume_min,
                'volume_max': info.volume_max,
                'volume_step': info.volume_step
            }
            
        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {str(e)}")
            return None
    
    def get_account_info(self):
        """
        Get account information
        
        Returns:
            dict: Account information
        """
        try:
            account = mt5.account_info()
            if account is None:
                logger.error("Failed to get account info")
                return None
            
            return {
                'login': account.login,
                'balance': account.balance,
                'equity': account.equity,
                'margin': account.margin,
                'free_margin': account.margin_free,
                'margin_level': account.margin_level,
                'currency': account.currency
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            return None
    
    def shutdown(self):
        """
        Shutdown MT5 connection
        """
        try:
            mt5.shutdown()
            self.connected = False
            logger.info("MT5 connection closed")
        except Exception as e:
            logger.error(f"Error shutting down MT5: {str(e)}")
