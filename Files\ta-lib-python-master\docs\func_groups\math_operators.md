# Math Operator Functions
### ADD - Vector Arithmetic Add
```python
real = ADD(real0, real1)
```

### DIV - Vector Arithmetic Div
```python
real = DIV(real0, real1)
```

### MAX - Highest value over a specified period
```python
real = MAX(real, timeperiod=30)
```

### MAXINDEX - Index of highest value over a specified period
```python
integer = MAXINDEX(real, timeperiod=30)
```

### MIN - Lowest value over a specified period
```python
real = MIN(real, timeperiod=30)
```

### MININDEX - Index of lowest value over a specified period
```python
integer = MININDEX(real, timeperiod=30)
```

### MINMAX - Lowest and highest values over a specified period
```python
min, max = MINMAX(real, timeperiod=30)
```

### MINMAXINDEX - Indexes of lowest and highest values over a specified period
```python
minidx, maxidx = MINMAXINDEX(real, timeperiod=30)
```

### MULT - Vector Arithmetic Mult
```python
real = MULT(real0, real1)
```

### SUB - Vector Arithmetic Subtraction
```python
real = SUB(real0, real1)
```

### SUM - Summation
```python
real = SUM(real, timeperiod=30)
```


[Documentation Index](../doc_index.md)

[FLOAT_RIGHTAll Function Groups](../funcs.md)
