name: Tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13", "3.14"]
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        allow-prereleases: true

    - name: Install dependencies
      run: |
        pip install --upgrade pip wheel setuptools
        pip install -r requirements_test.txt
        pip install flake8
        ./tools/build_talib_from_source.bash $DEPS_PATH
      env:
        DEPS_PATH: ${{ github.workspace }}/dependencies
        TA_INCLUDE_PATH: ${{ github.workspace }}/dependencies/include
        TA_LIBRARY_PATH: ${{ github.workspace }}/dependencies/lib

    - name: Build cython modules in-place
      run: |
        python setup.py build_ext --inplace --include-dirs=$TA_INCLUDE_PATH --library-dirs=$TA_LIBRARY_PATH
      env:
        TA_INCLUDE_PATH: ${{ github.workspace }}/dependencies/include
        TA_LIBRARY_PATH: ${{ github.workspace }}/dependencies/lib

    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        #flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Test with pytest
      run: |
        PYTHONPATH=. pytest
      env:
        LD_LIBRARY_PATH: ${{ github.workspace }}/dependencies/lib
