# Cycle Indicator Functions
### HT_DCPERIOD - Hilbert Transform - Dominant Cycle Period
NOTE: The ``HT_DCPERIOD`` function has an unstable period.  
```python
real = HT_DCPERIOD(real)
```

### HT_DCPHASE - Hilbert Transform - Dominant Cycle Phase
NOTE: The ``HT_DCPHASE`` function has an unstable period.  
```python
real = HT_DCPHASE(real)
```

### HT_PHASOR - Hilbert Transform - Phasor Components
NOTE: The ``HT_PHASOR`` function has an unstable period.  
```python
inphase, quadrature = HT_PHASOR(real)
```

### HT_SINE - Hilbert Transform - SineWave
NOTE: The ``HT_SINE`` function has an unstable period.  
```python
sine, leadsine = HT_SINE(real)
```

### HT_TRENDMODE - Hilbert Transform - Trend vs Cycle Mode
NOTE: The ``HT_TRENDMODE`` function has an unstable period.  
```python
integer = HT_TRENDMODE(real)
```


[Documentation Index](../doc_index.md)

[FLOAT_RIGHTAll Function Groups](../funcs.md)
