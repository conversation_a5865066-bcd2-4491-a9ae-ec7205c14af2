# Pattern Recognition Functions
### CDL2CROWS - Two Crows
```python
integer = CDL2CROWS(open, high, low, close)
```

### CDL3BLACKCROWS - Three Black Crows
```python
integer = CDL3BLACKCROWS(open, high, low, close)
```

### CDL3INSIDE - Three Inside Up/Down
```python
integer = CDL3INSIDE(open, high, low, close)
```

### CDL3LINESTRIKE - Three-Line Strike 
```python
integer = CDL3LINESTRIKE(open, high, low, close)
```

### CDL3OUTSIDE - Three Outside Up/Down
```python
integer = CDL3OUTSIDE(open, high, low, close)
```

### CDL3STARSINSOUTH - Three Stars In The South
```python
integer = CDL3STARSINSOUTH(open, high, low, close)
```

### CDL3WHITESOLDIERS - Three Advancing White Soldiers
```python
integer = CDL3WHITESOLDIERS(open, high, low, close)
```

### CDLABANDONEDBABY - Abandoned Baby
```python
integer = CD<PERSON><PERSON><PERSON>ONEDBABY(open, high, low, close, penetration=0)
```

### CDLADVANCEBLOCK - Advance Block
```python
integer = CDLADVANCEBLOCK(open, high, low, close)
```

### CDLBELTHOLD - Belt-hold
```python
integer = CDLBELTHOLD(open, high, low, close)
```

### CDLBREAKAWAY - Breakaway
```python
integer = CDLBREAKAWAY(open, high, low, close)
```

### CDLCLOSINGMARUBOZU - Closing Marubozu
```python
integer = CDLCLOSINGMARUBOZU(open, high, low, close)
```

### CDLCONCEALBABYSWALL - Concealing Baby Swallow
```python
integer = CDLCONCEALBABYSWALL(open, high, low, close)
```

### CDLCOUNTERATTACK - Counterattack
```python
integer = CDLCOUNTERATTACK(open, high, low, close)
```

### CDLDARKCLOUDCOVER - Dark Cloud Cover
```python
integer = CDLDARKCLOUDCOVER(open, high, low, close, penetration=0)
```

### CDLDOJI - Doji
```python
integer = CDLDOJI(open, high, low, close)
```

### CDLDOJISTAR - Doji Star
```python
integer = CDLDOJISTAR(open, high, low, close)
```

### CDLDRAGONFLYDOJI - Dragonfly Doji
```python
integer = CDLDRAGONFLYDOJI(open, high, low, close)
```

### CDLENGULFING - Engulfing Pattern
```python
integer = CDLENGULFING(open, high, low, close)
```

### CDLEVENINGDOJISTAR - Evening Doji Star
```python
integer = CDLEVENINGDOJISTAR(open, high, low, close, penetration=0)
```

### CDLEVENINGSTAR - Evening Star
```python
integer = CDLEVENINGSTAR(open, high, low, close, penetration=0)
```

### CDLGAPSIDESIDEWHITE - Up/Down-gap side-by-side white lines
```python
integer = CDLGAPSIDESIDEWHITE(open, high, low, close)
```

### CDLGRAVESTONEDOJI - Gravestone Doji
```python
integer = CDLGRAVESTONEDOJI(open, high, low, close)
```

### CDLHAMMER - Hammer
```python
integer = CDLHAMMER(open, high, low, close)
```

### CDLHANGINGMAN - Hanging Man
```python
integer = CDLHANGINGMAN(open, high, low, close)
```

### CDLHARAMI - Harami Pattern
```python
integer = CDLHARAMI(open, high, low, close)
```

### CDLHARAMICROSS - Harami Cross Pattern
```python
integer = CDLHARAMICROSS(open, high, low, close)
```

### CDLHIGHWAVE - High-Wave Candle
```python
integer = CDLHIGHWAVE(open, high, low, close)
```

### CDLHIKKAKE - Hikkake Pattern
```python
integer = CDLHIKKAKE(open, high, low, close)
```

### CDLHIKKAKEMOD - Modified Hikkake Pattern
```python
integer = CDLHIKKAKEMOD(open, high, low, close)
```

### CDLHOMINGPIGEON - Homing Pigeon
```python
integer = CDLHOMINGPIGEON(open, high, low, close)
```

### CDLIDENTICAL3CROWS - Identical Three Crows
```python
integer = CDLIDENTICAL3CROWS(open, high, low, close)
```

### CDLINNECK - In-Neck Pattern
```python
integer = CDLINNECK(open, high, low, close)
```

### CDLINVERTEDHAMMER - Inverted Hammer
```python
integer = CDLINVERTEDHAMMER(open, high, low, close)
```

### CDLKICKING - Kicking
```python
integer = CDLKICKING(open, high, low, close)
```

### CDLKICKINGBYLENGTH - Kicking - bull/bear determined by the longer marubozu
```python
integer = CDLKICKINGBYLENGTH(open, high, low, close)
```

### CDLLADDERBOTTOM - Ladder Bottom
```python
integer = CDLLADDERBOTTOM(open, high, low, close)
```

### CDLLONGLEGGEDDOJI - Long Legged Doji
```python
integer = CDLLONGLEGGEDDOJI(open, high, low, close)
```

### CDLLONGLINE - Long Line Candle
```python
integer = CDLLONGLINE(open, high, low, close)
```

### CDLMARUBOZU - Marubozu
```python
integer = CDLMARUBOZU(open, high, low, close)
```

### CDLMATCHINGLOW - Matching Low
```python
integer = CDLMATCHINGLOW(open, high, low, close)
```

### CDLMATHOLD - Mat Hold
```python
integer = CDLMATHOLD(open, high, low, close, penetration=0)
```

### CDLMORNINGDOJISTAR - Morning Doji Star
```python
integer = CDLMORNINGDOJISTAR(open, high, low, close, penetration=0)
```

### CDLMORNINGSTAR - Morning Star
```python
integer = CDLMORNINGSTAR(open, high, low, close, penetration=0)
```

### CDLONNECK - On-Neck Pattern
```python
integer = CDLONNECK(open, high, low, close)
```

### CDLPIERCING - Piercing Pattern
```python
integer = CDLPIERCING(open, high, low, close)
```

### CDLRICKSHAWMAN - Rickshaw Man
```python
integer = CDLRICKSHAWMAN(open, high, low, close)
```

### CDLRISEFALL3METHODS - Rising/Falling Three Methods
```python
integer = CDLRISEFALL3METHODS(open, high, low, close)
```

### CDLSEPARATINGLINES - Separating Lines
```python
integer = CDLSEPARATINGLINES(open, high, low, close)
```

### CDLSHOOTINGSTAR - Shooting Star
```python
integer = CDLSHOOTINGSTAR(open, high, low, close)
```

### CDLSHORTLINE - Short Line Candle
```python
integer = CDLSHORTLINE(open, high, low, close)
```

### CDLSPINNINGTOP - Spinning Top
```python
integer = CDLSPINNINGTOP(open, high, low, close)
```

### CDLSTALLEDPATTERN - Stalled Pattern
```python
integer = CDLSTALLEDPATTERN(open, high, low, close)
```

### CDLSTICKSANDWICH - Stick Sandwich
```python
integer = CDLSTICKSANDWICH(open, high, low, close)
```

### CDLTAKURI - Takuri (Dragonfly Doji with very long lower shadow)
```python
integer = CDLTAKURI(open, high, low, close)
```

### CDLTASUKIGAP - Tasuki Gap
```python
integer = CDLTASUKIGAP(open, high, low, close)
```

### CDLTHRUSTING - Thrusting Pattern
```python
integer = CDLTHRUSTING(open, high, low, close)
```

### CDLTRISTAR - Tristar Pattern
```python
integer = CDLTRISTAR(open, high, low, close)
```

### CDLUNIQUE3RIVER - Unique 3 River
```python
integer = CDLUNIQUE3RIVER(open, high, low, close)
```

### CDLUPSIDEGAP2CROWS - Upside Gap Two Crows
```python
integer = CDLUPSIDEGAP2CROWS(open, high, low, close)
```

### CDLXSIDEGAP3METHODS - Upside/Downside Gap Three Methods
```python
integer = CDLXSIDEGAP3METHODS(open, high, low, close)
```


[Documentation Index](../doc_index.md)

[FLOAT_RIGHTAll Function Groups](../funcs.md)